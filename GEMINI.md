# GEMINI.md

## Project Overview

This project is a comprehensive drone detection and management API named "CoDDN_API". It is built with **NestJS**, a Node.js framework, and follows **Domain-Driven Design (DDD)** principles. The API provides real-time drone monitoring, alert zone management, authorization tracking, and comprehensive analytics for drone operations.

The architecture is modular, with the business logic organized into distinct domains such as `alertZones`, `drones`, `events`, and `organizations`. It uses **MongoDB** as its primary database, **Redis** for caching, **Apache Kafka** for messaging, and **Auth0** for authentication.

## Building and Running

### Prerequisites

- Node.js (v20.9.0 or higher)
- npm or yarn
- MongoDB
- Redis (optional)
- Auth0 account
- Python 3.11 (for simulation scripts)

### Installation

1.  **Install Node.js dependencies:**
    ```bash
    npm install
    ```
2.  **Install Python dependencies:**
    ```bash
    pip install -r ./pyScripts/requirements.txt
    ```

### Environment Configuration

Create a `.env.dev` file in the root directory with the necessary environment variables. A full list of variables can be found in the `README.md` file.

### Running the Application

-   **Development mode with hot reload:**
    ```bash
    npm run start:dev
    ```
-   **Production mode:**
    ```bash
    npm run start:prod
    ```
-   **Debug mode:**
    ```bash
    npm run start:debug
    ```

The API will be available at `http://localhost:3000` (or the configured `PORT`).

### Testing

-   **Unit tests:**
    ```bash
    npm run test
    ```
-   **End-to-end tests:**
    ```bash
    npm run test:e2e
    ```
-   **Test coverage:**
    ```bash
    npm run test:cov
    ```

## Development Conventions

### Code Style

The project uses **ESLint** and **Prettier** for code linting and formatting.

-   **Linting:**
    ```bash
    npm run lint
    ```
-   **Code formatting:**
    ```bash
    npm run format
    ```

### Architecture

The application follows **Domain-Driven Design (DDD)** principles. Each domain is organized into the following components:

-   **Controllers:** Handle HTTP requests and responses.
-   **Services:** Contain the business logic and domain operations.
-   **Schemas:** Define the MongoDB data models using Mongoose.
-   **Interfaces:** Provide TypeScript type definitions.
-   **Modules:** Configure the NestJS module.

### Authentication

All API endpoints are protected by **JWT authentication** via **Auth0**. The bearer token must be included in the `Authorization` header of all requests.
