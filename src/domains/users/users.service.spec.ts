import { Test, TestingModule } from '@nestjs/testing';
import { UsersService } from './users.service';
import { getModelToken } from '@nestjs/mongoose';
import { AlertZoneService } from '../alertZones/alertZone.service';
import { OrganizationService } from '../organizations/organization.service';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Auth0HelperService } from '../../utils/auth0Helper.serivce';
import { User } from './user.schema';
import { Model, Types } from 'mongoose';

describe('UsersService', () => {
  let service: UsersService;
  let model: Model<User>;

  const mockModel = {
    find: jest.fn(),
    findOne: jest.fn(),
    findOneAndUpdate: jest.fn(),
    deleteOne: jest.fn(),
  };

  const mockAlertZoneService = { findZonesForUser: jest.fn(), findZonesForOrganization: jest.fn() };
  const mockOrganizationService = { createOrUpdateByAuth0Id: jest.fn() };
  const mockCacheManager = { get: jest.fn(), set: jest.fn(), del: jest.fn() };
  const mockAuth0HelperService = { getManagementTokenResponse: jest.fn() };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        { provide: getModelToken(User.name), useValue: mockModel },
        { provide: AlertZoneService, useValue: mockAlertZoneService },
        { provide: OrganizationService, useValue: mockOrganizationService },
        { provide: CACHE_MANAGER, useValue: mockCacheManager },
        { provide: Auth0HelperService, useValue: mockAuth0HelperService },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);
    model = module.get<Model<User>>(getModelToken(User.name));

    mockModel.find.mockReturnValue({ exec: jest.fn().mockResolvedValue([]) });
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findOrCreateUser', () => {
    it('should find or create a user', async () => {
      const userDto = { sub: 'auth0|123', email: '<EMAIL>', _id: new Types.ObjectId(), _doc: {} };
      mockModel.findOneAndUpdate.mockReturnValue({ exec: jest.fn().mockResolvedValue(userDto) });
      mockAlertZoneService.findZonesForUser.mockResolvedValue([]);
      mockAlertZoneService.findZonesForOrganization.mockResolvedValue([]);
      
      const result = await service.findOrCreateUser(userDto);
      expect(result).toBeDefined();
    });
  });

  describe('findOne', () => {
    it('should find a user by sub', async () => {
      const sub = 'auth0|123';
      const user = { sub, toJSON: () => ({ sub }) };
      mockModel.findOne.mockReturnValue(user);
      const result = await service.findOne({ sub });
      expect(result.sub).toEqual(sub);
    });
  });

  describe('removeUserBySub', () => {
    it('should remove a user by sub', async () => {
      const sub = 'auth0|123';
      mockModel.deleteOne.mockResolvedValue({ deletedCount: 1 });
      const result = await service.removeUserBySub(sub);
      expect(result.deletedCount).toBe(1);
    });
  });
});
