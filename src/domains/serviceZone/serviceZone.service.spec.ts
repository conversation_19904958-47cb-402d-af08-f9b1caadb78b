import { Test, TestingModule } from '@nestjs/testing';
import { ServiceZoneService } from './serviceZone.service';
import { getModelToken } from '@nestjs/mongoose';
import { H3Service } from '../../utils/h3.service';
import { OrganizationService } from '../organizations/organization.service';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { User } from '../users/user.schema';
import { Model, Types } from 'mongoose';
import { BadRequestException } from '@nestjs/common';

describe('ServiceZoneService', () => {
  let service: ServiceZoneService;
  let model: Model<any>;

  const mockModel = {
    create: jest.fn(),
    findById: jest.fn(),
    find: jest.fn(),
    deleteOne: jest.fn(),
    findByIdAndUpdate: jest.fn(),
  };

  const mockH3Service = { getH3Index: jest.fn(), getNeighbors: jest.fn() };
  const mockOrganizationService = { findByAuth0Id: jest.fn() };
  const mockUserModel = { findOne: jest.fn() };
  const mockCacheManager = { get: jest.fn(), set: jest.fn(), del: jest.fn() };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ServiceZoneService,
        { provide: getModelToken('servicezones'), useValue: mockModel },
        { provide: H3Service, useValue: mockH3Service },
        { provide: OrganizationService, useValue: mockOrganizationService },
        { provide: getModelToken(User.name), useValue: mockUserModel },
        { provide: CACHE_MANAGER, useValue: mockCacheManager },
      ],
    }).compile();

    service = module.get<ServiceZoneService>(ServiceZoneService);
    model = module.get<Model<any>>(getModelToken('servicezones'));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createServiceZone', () => {
    it('should create a service zone', async () => {
      const zoneDto = { name: 'Test Zone', h3_indexes: ['85283473fffffff'] };
      mockModel.create.mockResolvedValue(zoneDto);
      const result = await service.createServiceZone(zoneDto);
      expect(result).toEqual(zoneDto);
    });

    it('should throw BadRequestException on duplicate key error', async () => {
        mockModel.create.mockRejectedValue({ code: 11000 });
        await expect(service.createServiceZone({})).rejects.toThrow(BadRequestException);
    });
  });

  describe('getServiceZoneById', () => {
    it('should return a service zone by id', async () => {
        const id = new Types.ObjectId().toHexString();
        mockModel.findById.mockResolvedValue({_id: id});
        const result = await service.getServiceZoneById(id);
        expect(result._id).toEqual(id);
    });
  });

  describe('deleteServiceZoneById', () => {
    it('should delete a service zone', async () => {
        const id = new Types.ObjectId().toHexString();
        jest.spyOn(service, 'getServiceZoneById').mockResolvedValue({_id: id});
        mockModel.deleteOne.mockResolvedValue({ deletedCount: 1 });
        const result = await service.deleteServiceZoneById(id);
        expect(result.deletedCount).toBe(1);
    });
  });

  describe('updateServiceZoneById', () => {
    it('should update a service zone', async () => {
        const id = new Types.ObjectId().toHexString();
        const zoneDto = { name: 'Updated Name' };
        jest.spyOn(service, 'getServiceZoneById').mockResolvedValue({_id: id});
        mockModel.findByIdAndUpdate.mockResolvedValue({_id: id, ...zoneDto});
        const result = await service.updateServiceZoneById(id, zoneDto);
        expect(result.name).toEqual('Updated Name');
    });
  });
});
