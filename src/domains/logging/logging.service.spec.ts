import { Test, TestingModule } from '@nestjs/testing';
import { LoggingService } from './logging.service';
import { getModelToken } from '@nestjs/mongoose';
import { OrganizationsModel } from '../organizations/organization.model';
import { Model, Types } from 'mongoose';
import { Log } from './log.interface';
import { LogActionEnum } from '../../common/enums/LogActionEnum';
import { LogEntityEnum } from '../../common/enums/LogEntityEnum';

describe('LoggingService', () => {
  let service: LoggingService;
  let model: Model<Log>;
  let organizationsModel: OrganizationsModel;

  const mockModel = jest.fn().mockImplementation((dto) => ({
    ...dto,
    save: jest.fn().mockResolvedValue(dto),
  }));

  Object.assign(mockModel, {
    find: jest.fn().mockReturnValue({
      sort: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      populate: jest.fn().mockReturnThis(),
      exec: jest.fn().mockResolvedValue([]),
    }),
    countDocuments: jest.fn().mockReturnValue({ exec: jest.fn().mockResolvedValue(0) }),
  });

  const mockOrganizationsModel = {
    findOne: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LoggingService,
        { provide: getModelToken('logs'), useValue: mockModel },
        { provide: OrganizationsModel, useValue: mockOrganizationsModel },
      ],
    }).compile();

    service = module.get<LoggingService>(LoggingService);
    model = module.get<Model<Log>>(getModelToken('logs'));
    organizationsModel = module.get<OrganizationsModel>(OrganizationsModel);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createLog', () => {
    it('should create and save a log entry', async () => {
      const logData = {
        action: LogActionEnum.CREATE,
        entity: LogEntityEnum.SENSOR,
        entityId: new Types.ObjectId(),
        userId: new Types.ObjectId(),
      };
      
      const result = await service.createLog(logData.action, logData.entity, logData.entityId, logData.userId);
      
      expect(mockModel).toHaveBeenCalledWith(expect.objectContaining({
          action: logData.action,
          entity: logData.entity,
          entityId: logData.entityId,
          userId: logData.userId,
      }));
      expect(result).toEqual(expect.objectContaining(logData));
    });

    it('should handle organization id conversion', async () => {
        const orgAuth0Id = 'auth0-org-id';
        const orgMongoId = new Types.ObjectId();
        
        mockOrganizationsModel.findOne.mockResolvedValue({ _id: orgMongoId });

        await service.createLog(LogActionEnum.CREATE, LogEntityEnum.SENSOR, new Types.ObjectId(), new Types.ObjectId(), {}, orgAuth0Id);

        expect(mockOrganizationsModel.findOne).toHaveBeenCalledWith({ auth0_id: orgAuth0Id });
        expect(mockModel).toHaveBeenCalledWith(expect.objectContaining({ orgId: orgMongoId }));
    });
  });

  describe('getLogsForEntity', () => {
    it('should retrieve logs for a specific entity', async () => {
      await service.getLogsForEntity(LogEntityEnum.SENSOR, new Types.ObjectId().toHexString());
      expect((mockModel as any).find).toHaveBeenCalled();
      expect((mockModel as any).countDocuments).toHaveBeenCalled();
    });
  });
});
