import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { SensorService } from './sensor.service';
import Constants from 'src/common/constants';
import { LoggingService } from '../logging/logging.service';
import { Model, Types } from 'mongoose';
import { Sensor } from './sensor.interface';
import { NotFoundException, BadRequestException } from '@nestjs/common';

describe('SensorService', () => {
  let service: SensorService;
  let model: Model<Sensor>;
  let mockCacheManager: any;
  let mockLoggingService: any;

  const mockModel = jest.fn().mockImplementation(() => ({
    save: jest.fn(),
  }));

  Object.assign(mockModel, {
    findOne: jest.fn(),
    aggregate: jest.fn(),
    countDocuments: jest.fn(),
    findByIdAndUpdate: jest.fn(),
  });

  beforeEach(async () => {
    mockCacheManager = {
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn(),
    };

    mockLoggingService = {
      createLog: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SensorService,
        {
          provide: getModelToken(Constants.sensorProfile),
          useValue: mockModel,
        },
        {
          provide: CACHE_MANAGER,
          useValue: mockCacheManager,
        },
        {
          provide: LoggingService,
          useValue: mockLoggingService,
        }
      ],
    }).compile();

    service = module.get<SensorService>(SensorService);
    model = module.get<Model<Sensor>>(getModelToken(Constants.sensorProfile));

    jest.spyOn(model, 'findOne').mockReturnValue({ exec: jest.fn().mockResolvedValue(null) } as any);
    jest.spyOn(model, 'aggregate').mockReturnValue({ exec: jest.fn().mockResolvedValue([])} as any);
    jest.spyOn(model, 'countDocuments').mockReturnValue({ exec: jest.fn().mockResolvedValue(0)} as any);
    jest.spyOn(model, 'findByIdAndUpdate').mockReturnValue({ exec: jest.fn().mockResolvedValue({}) } as any);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a new sensor', async () => {
      const createDto = { NODE_ID: 'node-1' };
      const userId = new Types.ObjectId().toHexString();
      const savedSensor = { ...createDto, _id: new Types.ObjectId(), createdBy: userId, updatedBy: userId };

      jest.spyOn(model, 'findOne').mockReturnValue({ exec: () => Promise.resolve(null) } as any);

      // Mock the constructor to return an instance with save method
      (model as any).mockImplementation(() => ({
        save: jest.fn().mockResolvedValue(savedSensor),
      }));

      const result = await service.create(createDto as any, userId);
      expect(result).toEqual(savedSensor);
    });

    it('should throw BadRequestException if sensor with NODE_ID already exists', async () => {
      const createDto = { NODE_ID: 'node-1' };
      const userId = new Types.ObjectId().toHexString();
      jest.spyOn(model, 'findOne').mockReturnValue({ exec: () => Promise.resolve({}) } as any);
      await expect(service.create(createDto as any, userId)).rejects.toThrow(BadRequestException);
    });
  });

  describe('findOne', () => {
    it('should retrieve a single sensor by ID', async () => {
      const sensorId = new Types.ObjectId().toHexString();
      const mockSensor = { _id: sensorId };
      jest.spyOn(model, 'findOne').mockReturnValue({ exec: () => Promise.resolve(mockSensor) } as any);

      const result = await service.findOne(sensorId);
      expect(result).toEqual(mockSensor);
    });
  });

  describe('findAll', () => {
    it('should return a paginated list of sensors', async () => {
        const mockSensors = [{_id: '1'}, {_id: '2'}];
        const total = 2;
        jest.spyOn(model, 'aggregate').mockReturnValue({ exec: () => Promise.resolve(mockSensors) } as any);
        jest.spyOn(model, 'countDocuments').mockReturnValue({ exec: () => Promise.resolve(total) } as any);

        const result = await service.findAll(1, 10);
        expect(result.sensors).toEqual(mockSensors);
        expect(result.total).toEqual(total);
    });
  });

  describe('delete', () => {
    it('should soft delete a sensor', async () => {
        const sensorId = new Types.ObjectId().toHexString();
        const userId = new Types.ObjectId().toHexString();
        jest.spyOn(model, 'findOne').mockReturnValue({ exec: () => Promise.resolve({_id: sensorId}) } as any);
        jest.spyOn(model, 'findByIdAndUpdate').mockReturnValue({ exec: () => Promise.resolve({}) } as any);

        const result = await service.delete(sensorId, userId);
        expect(result.message).toContain('deleted successfully');
    });
  });

  // Existing tests for getSensorStats
  describe('getSensorStats', () => {
    it('should return sensor statistics', async () => {
      const mockStats = [
        {
          totalSensors: 128,
          onlineSensors: 112,
          offlineSensors: 16,
        },
      ];

      jest.spyOn(model, 'aggregate').mockReturnValue({ exec: () => Promise.resolve(mockStats)} as any);
      mockCacheManager.get.mockResolvedValue(null);

      const result = await service.getSensorStats();

      expect(result).toEqual({
        totalSensors: 128,
        onlineSensors: 112,
        offlineSensors: 16,
      });

    });

    it('should return default values when no sensors exist', async () => {
      jest.spyOn(model, 'aggregate').mockReturnValue({ exec: () => Promise.resolve([])} as any);
      mockCacheManager.get.mockResolvedValue(null);

      const result = await service.getSensorStats();

      expect(result).toEqual({
        totalSensors: 0,
        onlineSensors: 0,
        offlineSensors: 0,
      });
    });

    it('should return cached result when available', async () => {
      const cachedStats = {
        totalSensors: 100,
        onlineSensors: 80,
        offlineSensors: 20,
      };

      process.env.ENABLE_CACHE = 'true';
      mockCacheManager.get.mockResolvedValue(cachedStats);

      // Clear previous mock calls
      (model as any).aggregate.mockClear();

      const result = await service.getSensorStats();

      expect(result).toEqual(cachedStats);
      expect((model as any).aggregate).not.toHaveBeenCalled();
      process.env.ENABLE_CACHE = 'false'; // cleanup
    });
  });
});