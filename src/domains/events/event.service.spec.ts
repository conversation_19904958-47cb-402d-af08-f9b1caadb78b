import { Test, TestingModule } from '@nestjs/testing';
import { EventService } from './event.service';
import { getModelToken } from '@nestjs/mongoose';
import { EventProfileService } from './eventProfile.service';
import { EventHistoryService } from './eventHistory.service';
import { OrganizationService } from '../organizations/organization.service';
import Constants from '../../common/constants';

describe('EventService', () => {
  let service: EventService;
  let eventProfileService: EventProfileService;
  let eventHistoryService: EventHistoryService;
  let organizationService: OrganizationService;

  const mockNotificationModel = {
    aggregate: jest.fn(),
  };

  const mockEventProfileService = {
    findOne: jest.fn(),
    findByDeviceId: jest.fn(),
    findByDateRange: jest.fn(),
    getEventStats: jest.fn(),
  };

  const mockEventHistoryService = {
    findByEventId: jest.fn(),
    findByDeviceId: jest.fn(),
    findByDateRange: jest.fn(),
    getEventHistoryStats: jest.fn(),
  };

  const mockOrganizationService = {
    findByAuth0Id: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EventService,
        {
          provide: getModelToken(Constants.notification),
          useValue: mockNotificationModel,
        },
        { provide: EventProfileService, useValue: mockEventProfileService },
        { provide: EventHistoryService, useValue: mockEventHistoryService },
        { provide: OrganizationService, useValue: mockOrganizationService },
      ],
    }).compile();

    service = module.get<EventService>(EventService);
    eventProfileService = module.get<EventProfileService>(EventProfileService);
    eventHistoryService = module.get<EventHistoryService>(EventHistoryService);
    organizationService = module.get<OrganizationService>(OrganizationService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findOne', () => {
    it('should retrieve an event profile and its history', async () => {
      const eventId = 'test-event';
      const mockProfile = { EVENT_ID: eventId };
      const mockHistory = [{ some: 'data' }];
      
      jest.spyOn(eventProfileService, 'findOne').mockResolvedValue(mockProfile as any);
      jest.spyOn(eventHistoryService, 'findByEventId').mockResolvedValue(mockHistory as any);

      const result = await service.findOne(eventId, 10);

      expect(eventProfileService.findOne).toHaveBeenCalledWith(eventId);
      expect(eventHistoryService.findByEventId).toHaveBeenCalledWith(eventId, 10);
      expect(result.PROCESSED_ITEMS).toEqual(mockHistory);
    });
  });

  describe('getEventsByDeviceId', () => {
    it('should retrieve profiles and history for a device', async () => {
      const deviceId = 'test-device';
      jest.spyOn(eventProfileService, 'findByDeviceId').mockResolvedValue([]);
      jest.spyOn(eventHistoryService, 'findByDeviceId').mockResolvedValue([]);
      
      await service.getEventsByDeviceId(deviceId);

      expect(eventProfileService.findByDeviceId).toHaveBeenCalledWith(deviceId);
      expect(eventHistoryService.findByDeviceId).toHaveBeenCalledWith(deviceId);
    });
  });

  describe('getEventStatistics', () => {
    it('should retrieve statistics from both profile and history services', async () => {
      const mockFilters = { some: 'filter' };
      jest.spyOn(eventProfileService, 'getEventStats').mockResolvedValue({ total: 10 });
      jest.spyOn(eventHistoryService, 'getEventHistoryStats').mockResolvedValue({ total: 100 });

      const result = await service.getEventStatistics(mockFilters);

      expect(eventProfileService.getEventStats).toHaveBeenCalledWith(mockFilters);
      expect(eventHistoryService.getEventHistoryStats).toHaveBeenCalled();
      expect(result.profiles).toEqual({ total: 10 });
      expect(result.history).toEqual({ total: 100 });
    });
  });

});
