import { Test, TestingModule } from '@nestjs/testing';
import { EventHistoryService } from './eventHistory.service';
import { getModelToken } from '@nestjs/mongoose';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Model, Types } from 'mongoose';
import { EventHistory } from './eventHistory.interface';
import Constants from '../../common/constants';
import { NotFoundException } from '@nestjs/common';

describe('EventHistoryService', () => {
  let service: EventHistoryService;
  let model: Model<EventHistory>;

  const mockModel = jest.fn().mockImplementation((dto) => ({
    ...dto,
    save: jest.fn().mockResolvedValue(dto),
  }));

  Object.assign(mockModel, {
    find: jest.fn().mockReturnValue({
      sort: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      exec: jest.fn().mockResolvedValue([]),
    }),
    findById: jest.fn().mockReturnValue({ exec: jest.fn() }),
    findByIdAndUpdate: jest.fn().mockReturnValue({ exec: jest.fn() }),
    findByIdAndDelete: jest.fn().mockReturnValue({ exec: jest.fn() }),
    countDocuments: jest.fn().mockReturnValue({ exec: jest.fn() }),
    insertMany: jest.fn(),
  });

  const mockCacheManager = { get: jest.fn(), set: jest.fn(), del: jest.fn() };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EventHistoryService,
        { provide: getModelToken(Constants.event_history), useValue: mockModel },
        { provide: CACHE_MANAGER, useValue: mockCacheManager },
      ],
    }).compile();

    service = module.get<EventHistoryService>(EventHistoryService);
    model = module.get<Model<EventHistory>>(getModelToken(Constants.event_history));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create and save a new event history', async () => {
        const historyDto = { EVENT_ID: 'evt-1' };
        const result = await service.create(historyDto);
        expect(result).toEqual(historyDto);
    });
  });

  describe('findOne', () => {
    it('should find an event history by id', async () => {
        const id = new Types.ObjectId().toHexString();
        (mockModel as any).findById.mockReturnValue({ exec: jest.fn().mockResolvedValue({_id: id}) });
        const result = await service.findOne(id);
        expect(result._id).toEqual(id);
    });

    it('should throw NotFoundException if history not found', async () => {
        const id = new Types.ObjectId().toHexString();
        (mockModel as any).findById.mockReturnValue({ exec: jest.fn().mockResolvedValue(null) });
        await expect(service.findOne(id)).rejects.toThrow(NotFoundException);
    });
  });
  
  describe('findByEventId', () => {
    it('should query for event history by event ID', async () => {
        const eventId = 'evt-1';
        await service.findByEventId(eventId);
        expect((mockModel as any).find).toHaveBeenCalledWith({ EVENT_ID: eventId });
    });
  });

  describe('delete', () => {
    it('should delete an event history', async () => {
        const id = new Types.ObjectId().toHexString();
        (mockModel as any).findById.mockReturnValue({ exec: jest.fn().mockResolvedValue({_id: id, EVENT_ID: '1', DEVICE_ID: '1'}) });
        (mockModel as any).findByIdAndDelete.mockReturnValue({ exec: jest.fn().mockResolvedValue({}) });
        const result = await service.delete(id);
        expect(result.message).toContain('deleted successfully');
    });
  });
});
