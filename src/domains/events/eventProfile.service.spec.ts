import { Test, TestingModule } from '@nestjs/testing';
import { EventProfileService } from './eventProfile.service';
import { getModelToken } from '@nestjs/mongoose';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Model, Types } from 'mongoose';
import { EventProfile } from './eventProfile.interface';
import Constants from '../../common/constants';
import { NotFoundException } from '@nestjs/common';

describe('EventProfileService', () => {
  let service: EventProfileService;
  let model: Model<EventProfile>;

  const mockModel = jest.fn().mockImplementation(() => ({
    save: jest.fn(),
  }));

  Object.assign(mockModel, {
    find: jest.fn(),
    findOne: jest.fn(),
    findOneAndUpdate: jest.fn(),
    countDocuments: jest.fn(),
    aggregate: jest.fn(),
  });

  const mockCacheManager = { get: jest.fn(), set: jest.fn(), del: jest.fn() };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EventProfileService,
        { provide: getModelToken(Constants.event_profile), useValue: mockModel },
        { provide: CACHE_MANAGER, useValue: mockCacheManager },
      ],
    }).compile();

    service = module.get<EventProfileService>(EventProfileService);
    model = module.get<Model<EventProfile>>(getModelToken(Constants.event_profile));

    (mockModel as any).find.mockReturnValue({
        sort: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue([]),
      });
    (mockModel as any).findOne.mockReturnValue({ exec: jest.fn().mockResolvedValue({}) } as any);
    (mockModel as any).findOneAndUpdate.mockReturnValue({ exec: jest.fn().mockResolvedValue({}) } as any);
    (mockModel as any).countDocuments.mockReturnValue({ exec: jest.fn().mockResolvedValue(0) } as any);
    (mockModel as any).aggregate.mockReturnValue({ exec: jest.fn().mockResolvedValue([]) } as any);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create and save a new event profile', async () => {
        const profileDto = { EVENT_ID: 'evt-1', DEVICE_ID: 'device-1' };
        const savedProfile = { _id: new Types.ObjectId(), ...profileDto };

        (mockModel as any).mockImplementation(() => ({
          save: jest.fn().mockResolvedValue(savedProfile),
        }));

        const result = await service.create(profileDto);
        expect(result).toEqual(savedProfile);
    });
  });

  describe('findOne', () => {
    it('should find an event profile by id', async () => {
        const eventId = 'evt-1';
        (mockModel as any).findOne.mockReturnValue({ exec: jest.fn().mockResolvedValue({EVENT_ID: eventId}) });
        const result = await service.findOne(eventId);
        expect(result.EVENT_ID).toEqual(eventId);
    });

    it('should throw NotFoundException if profile not found', async () => {
        const eventId = 'evt-1';
        (mockModel as any).findOne.mockReturnValue({ exec: jest.fn().mockResolvedValue(null) });
        await expect(service.findOne(eventId)).rejects.toThrow(NotFoundException);
    });
  });
  
  describe('delete', () => {
    it('should soft delete an event profile', async () => {
        const eventId = 'evt-1';
        (mockModel as any).findOne.mockReturnValue({ exec: jest.fn().mockResolvedValue({EVENT_ID: eventId}) });
        (mockModel as any).findOneAndUpdate.mockReturnValue({ exec: jest.fn().mockResolvedValue({}) });
        const result = await service.delete(eventId);
        expect(result.message).toContain('deleted successfully');
    });
  });

  describe('getEventStats', () => {
      it('should return aggregated event stats', async () => {
          const stats = { totalEvents: 5 };
          (mockModel as any).aggregate.mockReturnValue({ exec: jest.fn().mockResolvedValue([stats]) });
          const result = await service.getEventStats();
          expect(result).toEqual(stats);
      })
  })
});
