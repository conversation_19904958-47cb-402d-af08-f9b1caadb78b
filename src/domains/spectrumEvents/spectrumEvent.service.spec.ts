import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { SpectrumEventService } from './spectrumEvent.service';
import Constants from 'src/common/constants';
import { OrganizationService } from 'src/domains/organizations/organization.service';
import { Model, Types } from 'mongoose';
import { SpectrumEvent } from './spectrumEvent.interface';

describe('SpectrumEventService', () => {
  let service: SpectrumEventService;
  let model: Model<SpectrumEvent>;
  let organizationService: OrganizationService;

  const mockModel = jest.fn().mockImplementation(() => ({
    save: jest.fn(),
  }));

  Object.assign(mockModel, {
    findById: jest.fn(),
    find: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    countDocuments: jest.fn(),
    aggregate: jest.fn(),
  });

  const mockOrganizationService = {
      findByAuth0Id: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SpectrumEventService,
        {
          provide: getModelToken(Constants.spectrum_profile_events, 'spectrum'),
          useValue: mockModel,
        },
        {
            provide: OrganizationService,
            useValue: mockOrganizationService,
        }
      ],
    }).compile();

    service = module.get<SpectrumEventService>(SpectrumEventService);
    model = module.get<Model<SpectrumEvent>>(getModelToken(Constants.spectrum_profile_events, 'spectrum'));
    organizationService = module.get<OrganizationService>(OrganizationService);

    (mockModel as any).findById.mockReturnValue({ exec: jest.fn().mockResolvedValue({}) } as any);
    (mockModel as any).aggregate.mockResolvedValue([]);
    (mockModel as any).findByIdAndUpdate.mockReturnValue({ exec: jest.fn().mockResolvedValue({}) } as any);
    (mockModel as any).countDocuments.mockReturnValue({ exec: jest.fn().mockResolvedValue(0) } as any);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createSpectrumEvent', () => {
    it('should create a spectrum event', async () => {
      const eventData: Partial<SpectrumEvent> = { event_id: 'evt-1' };
      const savedEvent = { _id: new Types.ObjectId(), ...eventData };

      (mockModel as any).mockImplementation(() => ({
        save: jest.fn().mockResolvedValue(savedEvent),
      }));

      const result = await service.createSpectrumEvent(eventData);
      expect(result).toEqual(savedEvent);
    });
  });

  describe('findSpectrumEventById', () => {
    it('should find a spectrum event by ID', async () => {
      const id = new Types.ObjectId().toHexString();
      (mockModel as any).findById.mockReturnValue({ exec: jest.fn().mockResolvedValue({ _id: id }) });
      const result = await service.findSpectrumEventById(id);
      expect(result._id).toEqual(id);
    });
  });

  describe('findSpectrumEventByEventId', () => {
    it('should find spectrum events by event ID', async () => {
      const eventId = 'evt-1';
      const orgId = 'org-1';
      mockOrganizationService.findByAuth0Id.mockResolvedValue({_id: new Types.ObjectId()});
      (mockModel as any).aggregate.mockResolvedValue([]);
      await service.findSpectrumEventByEventId(eventId, orgId);
      expect((mockModel as any).aggregate).toHaveBeenCalled();
    });
  });

  describe('deleteSpectrumEvent', () => {
    it('should soft delete a spectrum event', async () => {
        const id = new Types.ObjectId().toHexString();
        (mockModel as any).findByIdAndUpdate.mockReturnValue({ exec: jest.fn().mockResolvedValue({_id: id, isDeleted: true}) });
        const result = await service.deleteSpectrumEvent(id);
        expect(result.isDeleted).toBe(true);
    });
  });

  describe('getSpectrumEventCount', () => {
    it('should return the count of spectrum events', async () => {
        const count = 5;
        (mockModel as any).countDocuments.mockReturnValue({ exec: jest.fn().mockResolvedValue(count) });
        const result = await service.getSpectrumEventCount();
        expect(result).toEqual(count);
    });
  });
});