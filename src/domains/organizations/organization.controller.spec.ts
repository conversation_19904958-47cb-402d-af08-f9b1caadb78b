import { Test, TestingModule } from '@nestjs/testing';
import { OrganizationController } from './organization.controller';
import { OrganizationService } from './organization.service';
import { HttpException, HttpStatus } from '@nestjs/common';

describe('OrganizationController', () => {
  let controller: OrganizationController;
  let service: OrganizationService;

  beforeEach(async () => {
    const mockOrganizationService = {
      findAll: jest.fn(),
      findById: jest.fn(),
      findByAuth0Id: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      createOrUpdate: jest.fn(),
      delete: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [OrganizationController],
      providers: [
        {
          provide: OrganizationService,
          useValue: mockOrganizationService,
        },
      ],
    }).compile();

    controller = module.get<OrganizationController>(OrganizationController);
    service = module.get<OrganizationService>(OrganizationService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll', () => {
    it('should return paginated organizations', async () => {
      const mockOrganizations = [
        { _id: '1', name: 'Org 1', display_name: 'Organization 1' },
        { _id: '2', name: 'Org 2', display_name: 'Organization 2' },
      ];
      const mockResult = { organizations: mockOrganizations, total: 2 };

      jest.spyOn(service, 'findAll').mockResolvedValue(mockResult as any);

      const mockReq = { roles: ['ad-admin'] };
      const result = await controller.findAll(1, 10, {}, mockReq as any);

      expect(result).toEqual({
        organizations: mockOrganizations,
        total: 2,
        page: 1,
        totalPages: 1,
      });
      expect(service.findAll).toHaveBeenCalledWith({}, { page: 1, pageSize: 10 });
    });
  });

  describe('findOne', () => {
    it('should return an organization by ID', async () => {
      const mockOrganization = { _id: '1', name: 'Test Org', display_name: 'Test Organization' };

      jest.spyOn(service, 'findById').mockResolvedValue(mockOrganization as any);

      const result = await controller.findOne('1');

      expect(result).toEqual(mockOrganization);
      expect(service.findById).toHaveBeenCalledWith('1');
    });

    it('should throw HttpException when organization not found', async () => {
      jest.spyOn(service, 'findById').mockResolvedValue(null);

      await expect(controller.findOne('1')).rejects.toThrow(HttpException);
    });
  });

  describe('create', () => {
    it('should create a new organization', async () => {
      const createDto = { name: 'New Org', display_name: 'New Organization' };
      const mockOrganization = { _id: '1', ...createDto };

      jest.spyOn(service, 'create').mockResolvedValue(mockOrganization as any);

      const result = await controller.create(createDto);

      expect(result).toEqual(mockOrganization);
      expect(service.create).toHaveBeenCalledWith(createDto);
    });
  });

  describe('update', () => {
    it('should update an organization', async () => {
      const updateDto = { name: 'Updated Org' };
      const mockOrganization = { _id: '1', name: 'Updated Org', display_name: 'Test Organization' };

      jest.spyOn(service, 'update').mockResolvedValue(mockOrganization as any);

      const result = await controller.update('1', updateDto);

      expect(result).toEqual(mockOrganization);
      expect(service.update).toHaveBeenCalledWith('1', updateDto);
    });

    it('should throw HttpException when organization not found for update', async () => {
      const updateDto = { name: 'Updated Org' };

      jest.spyOn(service, 'update').mockResolvedValue(null);

      await expect(controller.update('1', updateDto)).rejects.toThrow(HttpException);
    });
  });

  describe('delete', () => {
    it('should delete an organization', async () => {
      jest.spyOn(service, 'delete').mockResolvedValue(true);

      const result = await controller.delete('1');

      expect(result).toEqual({ message: 'Organization deleted successfully' });
      expect(service.delete).toHaveBeenCalledWith('1');
    });

    it('should throw HttpException when organization not found for deletion', async () => {
      jest.spyOn(service, 'delete').mockResolvedValue(false);

      await expect(controller.delete('1')).rejects.toThrow(HttpException);
    });
  });
});
