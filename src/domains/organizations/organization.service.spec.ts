import { Test, TestingModule } from '@nestjs/testing';
import { OrganizationService } from './organization.service';
import { getModelToken } from '@nestjs/mongoose';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Auth0HelperService } from '../../utils/auth0Helper.serivce';
import { Model, Types } from 'mongoose';
import { Organizations } from './organization.interface';
import Constants from '../../common/constants';
import { NotFoundException } from '@nestjs/common';

describe('OrganizationService', () => {
  let service: OrganizationService;
  let model: Model<Organizations>;

  const mockModel = jest.fn().mockImplementation(() => ({
    save: jest.fn(),
  }));

  Object.assign(mockModel, {
    find: jest.fn(),
    findOne: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    findOneAndUpdate: jest.fn(),
    countDocuments: jest.fn(),
  });

  const mockCacheManager = { get: jest.fn(), set: jest.fn(), del: jest.fn() };
  const mockAuth0HelperService = { getManagementTokenResponse: jest.fn() };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OrganizationService,
        { provide: getModelToken(Constants.organizations), useValue: mockModel },
        { provide: CACHE_MANAGER, useValue: mockCacheManager },
        { provide: Auth0HelperService, useValue: mockAuth0HelperService },
      ],
    }).compile();

    service = module.get<OrganizationService>(OrganizationService);
    model = module.get<Model<Organizations>>(getModelToken(Constants.organizations));

    (mockModel as any).find.mockReturnValue({ exec: jest.fn().mockResolvedValue([]) });
    (mockModel as any).findOne.mockReturnValue({ exec: jest.fn().mockResolvedValue({}) });
    (mockModel as any).findById.mockReturnValue({ exec: jest.fn().mockResolvedValue({}) });
    (mockModel as any).findByIdAndUpdate.mockReturnValue({ exec: jest.fn().mockResolvedValue({}) });
    (mockModel as any).findByIdAndDelete.mockReturnValue({ exec: jest.fn().mockResolvedValue(true) });
    (mockModel as any).findOneAndUpdate.mockReturnValue({ exec: jest.fn().mockResolvedValue({}) });
    (mockModel as any).countDocuments.mockReturnValue({ exec: jest.fn().mockResolvedValue(0) });
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create and save a new organization', async () => {
      const orgDto = { name: 'Test Org', auth0_id: 'auth0|123' };
      const savedOrg = { _id: new Types.ObjectId(), ...orgDto };

      (mockModel as any).mockImplementation(() => ({
        save: jest.fn().mockResolvedValue(savedOrg),
      }));

      const result = await service.create(orgDto);
      expect(result).toEqual(savedOrg);
    });
  });

  describe('findById', () => {
    it('should find an organization by id', async () => {
      const id = new Types.ObjectId().toHexString();
      (model.findById as jest.Mock).mockReturnValue({ exec: jest.fn().mockResolvedValue({_id: id}) });
      const result = await service.findById(id);
      expect(result._id).toEqual(id);
    });
  });

  describe('update', () => {
    it('should update an organization', async () => {
        const id = new Types.ObjectId().toHexString();
        const orgDto = { name: 'Updated Org' };
        (model.findById as jest.Mock).mockReturnValue({ exec: jest.fn().mockResolvedValue({_id: id}) });
        (model.findByIdAndUpdate as jest.Mock).mockReturnValue({ exec: jest.fn().mockResolvedValue({_id: id, ...orgDto}) });
        const result = await service.update(id, orgDto);
        expect(result.name).toEqual('Updated Org');
    });

    it('should throw NotFoundException if organization to update is not found', async () => {
        const id = new Types.ObjectId().toHexString();
        (model.findById as jest.Mock).mockReturnValue({ exec: jest.fn().mockResolvedValue(null) });
        await expect(service.update(id, {})).rejects.toThrow(NotFoundException);
    });
  });

  describe('delete', () => {
    it('should delete an organization', async () => {
      const id = new Types.ObjectId().toHexString();
      (model.findById as jest.Mock).mockReturnValue({ exec: jest.fn().mockResolvedValue({_id: id}) });
      (model.findByIdAndDelete as jest.Mock).mockReturnValue({ exec: jest.fn().mockResolvedValue(true) });
      const result = await service.delete(id);
      expect(result).toBe(true);
    });
  });
});
