import { Injectable, NotFoundException, Inject, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { Organizations } from './organization.interface';
import Constants from 'src/common/constants';
import { CacheKeyPatterns } from '../../utils/cache.utils';
import axios from 'axios';
import { Auth0HelperService } from 'src/utils/auth0Helper.serivce';

@Injectable()
export class OrganizationService {
    private readonly logger = new Logger(OrganizationService.name);

  constructor(
    @InjectModel(Constants.organizations) private readonly organizationModel: Model<Organizations>,
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache,
    private readonly auth0HelperService: Auth0HelperService,
  ) {}

  async findAll(
    filter: any = {},
    pagination: { page: number; pageSize: number } = { page: 1, pageSize: 10 },
  ): Promise<{ organizations: Organizations[]; total: number }> {
    const { sortBy, sortOrder, ...filterParams } = filter;
    const mongoFilter = this.buildPatternMatchingFilter(filterParams);

    const sort: any = {};
    if (sortBy) {
      sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
    } else {
      sort['display_name'] = 1;
    }

    const skip = (pagination.page - 1) * pagination.pageSize;

    const organizationsQuery = this.organizationModel
      .find(mongoFilter)
      .sort(sort)
      .skip(skip)
      .limit(pagination.pageSize);

    const totalQuery = this.organizationModel.countDocuments(mongoFilter);

    const [organizations, total] = await Promise.all([
      organizationsQuery.exec(),
      totalQuery.exec(),
    ]);

    return { organizations, total };
  }

  private buildPatternMatchingFilter(filter: any): any {
    const mongoFilter = { ...filter };

    const stringFields = ['name', 'display_name', 'auth0_id'];

    for (const field of stringFields) {
      if (mongoFilter[field] && typeof mongoFilter[field] === 'string') {
        mongoFilter[field] = {
          $regex: mongoFilter[field],
          $options: 'i'
        };
      }
    }

    return mongoFilter;
  }

  async findOne(filter: any): Promise<Organizations | null> {
    return this.organizationModel.findOne(filter).exec();
  }

  async findById(id: string): Promise<Organizations | null> {
    if (!Types.ObjectId.isValid(id)) {
      return null;
    }
    return this.organizationModel.findById(id).exec();
  }

  async findByAuth0Id(auth0Id: string): Promise<Organizations | null> {
    const cacheKey = CacheKeyPatterns.ORG_BY_AUTH0_ID(auth0Id);
    // Try to get from cache first
    const cachedOrg = process.env.ENABLE_CACHE === 'true'? await this.cacheManager.get<Organizations>(cacheKey): undefined;
    if (cachedOrg && process.env.ENABLE_CACHE === 'true') {
      this.logger.log('Using cached organization');
      cachedOrg._id = new Types.ObjectId(cachedOrg._id);
      return cachedOrg;
    }

    this.logger.log('Fetching new organization');
    // If not in cache, query database
    const organization = await this.organizationModel.findOne({ auth0_id: auth0Id }).exec();

    if (organization && process.env.ENABLE_CACHE === 'true') {
      await this.cacheManager.set(cacheKey, organization, 300000); // 5 minutes TTL
    }

    return organization;
  }

  async createOrUpdateByAuth0Id(auth0Id: string, organizationData: any): Promise<Organizations> {
    const organization = await this.organizationModel
      .findOneAndUpdate(
        { auth0_id: auth0Id },
        {
          ...organizationData,
        },
        { new: true, upsert: true },
      )
      .exec();

    // Invalidate cache for this auth0Id
    if(process.env.ENABLE_CACHE === 'true') {
      const cacheKey = CacheKeyPatterns.ORG_BY_AUTH0_ID(auth0Id);
      await this.cacheManager.del(cacheKey);
    }

    return organization;
  }

  async create(organizationData: Partial<Organizations>): Promise<Organizations> {
    const newOrganization = new this.organizationModel(organizationData);
    const savedOrganization = await newOrganization.save();

    // Invalidate cache if auth0_id is provided (in case there was a cached null result)
    if (savedOrganization.auth0_id && process.env.ENABLE_CACHE === 'true') {
      const cacheKey = CacheKeyPatterns.ORG_BY_AUTH0_ID(savedOrganization.auth0_id);
      await this.cacheManager.del(cacheKey);
    }

    return savedOrganization;
  }

  async update(id: string, organizationData: Partial<Organizations>): Promise<Organizations | null> {
    if (!Types.ObjectId.isValid(id)) {
      throw new NotFoundException(`Invalid organization ID: ${id}`);
    }

    // Get the current organization to check for auth0_id changes
    const currentOrg = await this.organizationModel.findById(id).exec();
    if (!currentOrg) {
      throw new NotFoundException(`Organization with ID ${id} not found`);
    }

    const updatedOrganization = await this.organizationModel
      .findByIdAndUpdate(id, organizationData, { new: true })
      .exec();

    if (!updatedOrganization) {
      throw new NotFoundException(`Organization with ID ${id} not found`);
    }

    // Invalidate cache for the current auth0_id
    if (currentOrg.auth0_id && process.env.ENABLE_CACHE === 'true') {
      const currentCacheKey = CacheKeyPatterns.ORG_BY_AUTH0_ID(currentOrg.auth0_id);
      await this.cacheManager.del(currentCacheKey);
    }

    // If auth0_id was updated, also invalidate cache for the new auth0_id
    if (organizationData.auth0_id && organizationData.auth0_id !== currentOrg.auth0_id && process.env.ENABLE_CACHE === 'true') {
      const newCacheKey = CacheKeyPatterns.ORG_BY_AUTH0_ID(organizationData.auth0_id);
      await this.cacheManager.del(newCacheKey);
    }

    return updatedOrganization;
  }

  async delete(id: string): Promise<boolean> {
    if (!Types.ObjectId.isValid(id)) {
      throw new NotFoundException(`Invalid organization ID: ${id}`);
    }

    // Get the organization before deleting to invalidate cache
    const orgToDelete = await this.organizationModel.findById(id).exec();

    const result = await this.organizationModel.findByIdAndDelete(id).exec();

    // Invalidate cache if organization was found and deleted
    if (result && orgToDelete?.auth0_id && process.env.ENABLE_CACHE === 'true') {
      const cacheKey = CacheKeyPatterns.ORG_BY_AUTH0_ID(orgToDelete.auth0_id);
      await this.cacheManager.del(cacheKey);
    }

    return !!result;
  }

  async exists(filter: any): Promise<boolean> {
    const count = await this.organizationModel.countDocuments(filter).exec();
    return count > 0;
  }

  async syncOrgsWithAuth0() {
    const ManagementToken = await this.auth0HelperService.getManagementTokenResponse();

    const orgPrmise = axios.get(
      `${process.env.AUTH0_MANAGEMENT_API}/api/v2/organizations`,
      {
        headers: {
          Authorization: `Bearer ${ManagementToken.access_token}`,
        },
      },
    );

    const [orgs] = await Promise.all([orgPrmise]);

    orgs.data.forEach(async (org) => {
      await this.createOrUpdateByAuth0Id(org.id, org);
    });

    return orgs.data;
  }
}
