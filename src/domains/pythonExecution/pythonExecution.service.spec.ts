import { Test, TestingModule } from '@nestjs/testing';
import { PythonExecutionService } from './pythonExecution.service';
import { Logger } from '@nestjs/common';
import { spawn } from 'child_process';

jest.mock('child_process', () => ({
  spawn: jest.fn(),
}));

describe('PythonExecutionService', () => {
  let service: PythonExecutionService;
  const mockSpawn = spawn as jest.Mock;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [PythonExecutionService, Logger],
    }).compile();

    service = module.get<PythonExecutionService>(PythonExecutionService);

    mockSpawn.mockReturnValue({
        stdout: { on: jest.fn() },
        stderr: { on: jest.fn() },
        on: jest.fn(),
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('executeLiveSimulationScript', () => {
    it('should spawn the ridSimulation.py script with correct arguments', () => {
      const lat = 34.0;
      const lng = -118.0;
      const range = 100;

      service.executeLiveSimulationScript(lat, lng, range);

      expect(mockSpawn).toHaveBeenCalledWith(
        'python3',
        [expect.stringContaining('ridSimulation.py'), '-A', lat.toString(), '-O', lng.toString(), '-l', range.toString()],
        expect.any(Object)
      );
    });
  });

  describe('executeHeartbeatSimulationScript', () => {
    it('should spawn the heartbeatSim.py script with correct arguments', () => {
      const lat = '34.0';
      const lng = '-118.0';
      const length = '10';
      const time_gap = '5';
      const status = 'active';

      service.executeHeartbeatSimulationScript(lat, lng, length, time_gap, status);

      expect(mockSpawn).toHaveBeenCalledWith(
        'python3',
        [expect.stringContaining('heartbeatSim.py'), '-l', lat, '-o', lng, '-n', length, '-g', time_gap, '-s', status],
        expect.any(Object)
      );
    });
  });
});
