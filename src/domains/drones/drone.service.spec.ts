import { Test, TestingModule } from '@nestjs/testing';
import { DroneService } from './drone.service';
import { getModelToken } from '@nestjs/mongoose';
import { DroneAuthorizationService } from '../droneAuthorizations/droneAuthorization.service';
import { NotificationService } from '../alertNotifications/notification.service';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Model, Types } from 'mongoose';
import { Drone } from './drone.interface';
import Constants from '../../common/constants';
import { BadRequestException, NotFoundException } from '@nestjs/common';

describe('DroneService', () => {
  let service: DroneService;
  let model: Model<Drone>;
  let droneAuthorizationService: DroneAuthorizationService;
  let notificationService: NotificationService;

  const mockModel = jest.fn().mockImplementation((dto) => ({
    ...dto,
    save: jest.fn().mockResolvedValue(dto),
  }));

  Object.assign(mockModel, {
    find: jest.fn(),
    findOne: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    countDocuments: jest.fn(),
    aggregate: jest.fn(),
  });

  const mockDroneAuthService = {
    getLatestAuthorizationForDrone: jest.fn(),
    create: jest.fn(),
  };
  const mockNotificationService = {
    findByEventIdAndOrgId: jest.fn().mockResolvedValue([]),
    findAllActiveEventsByOrgId: jest.fn().mockResolvedValue([]),
    update: jest.fn(),
  };
  const mockCacheManager = { get: jest.fn(), set: jest.fn(), del: jest.fn() };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DroneService,
        { provide: getModelToken(Constants.drones), useValue: mockModel },
        { provide: DroneAuthorizationService, useValue: mockDroneAuthService },
        { provide: NotificationService, useValue: mockNotificationService },
        { provide: CACHE_MANAGER, useValue: mockCacheManager },
      ],
    }).compile();

    service = module.get<DroneService>(DroneService);
    model = module.get<Model<Drone>>(getModelToken(Constants.drones));
    droneAuthorizationService = module.get<DroneAuthorizationService>(DroneAuthorizationService);
    notificationService = module.get<NotificationService>(NotificationService);

    (mockModel as any).find.mockReturnValue({
        sort: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue([]),
    } as any);
    (mockModel as any).findOne.mockReturnValue({ exec: jest.fn().mockResolvedValue(null) } as any);
    (mockModel as any).findByIdAndUpdate.mockReturnValue({ exec: jest.fn().mockResolvedValue({}) } as any);
    (mockModel as any).aggregate.mockReturnValue({ exec: jest.fn().mockResolvedValue([])} as any);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a drone', async () => {
      const droneDto = { device_id: 'test-device' };
      const userId = new Types.ObjectId().toHexString();
      const userObjectId = new Types.ObjectId(userId);
      const expectedResult = {
        device_id: 'test-device',
        createdBy: userObjectId,
        updatedBy: userObjectId
      };

      jest.spyOn(model, 'findOne').mockReturnValue({ exec: jest.fn().mockResolvedValue(null) } as any);

      const result = await service.create(droneDto, userId);

      expect(result).toEqual(expectedResult);
    });

    it('should throw BadRequestException if drone already exists', async () => {
        const droneDto = { device_id: 'test-device' };
        const userId = new Types.ObjectId().toHexString();
        
        jest.spyOn(model, 'findOne').mockReturnValue({ exec: jest.fn().mockResolvedValue(droneDto) } as any);
  
        await expect(service.create(droneDto, userId)).rejects.toThrow(BadRequestException);
      });
  });

  describe('findOne', () => {
    it('should find a drone by id', async () => {
        const droneId = new Types.ObjectId();
        (mockModel as any).findOne.mockReturnValue({ exec: jest.fn().mockResolvedValue({_id: droneId}) });

        const result = await service.findOne(droneId.toHexString());
        expect(result._id).toEqual(droneId);
    });

    it('should throw NotFoundException if drone not found', async () => {
        const droneId = new Types.ObjectId();
        (mockModel as any).findOne.mockReturnValue({ exec: jest.fn().mockResolvedValue(null) });

        await expect(service.findOne(droneId.toHexString())).rejects.toThrow(NotFoundException);
    });
  });

  describe('createOrUpdateDroneWithAuthorization', () => {
      it('should create a new drone and authorization', async () => {
          const droneDto = { device_id: 'new-device' };
          const authDto = { is_authorized: true, org_id: 'org1', current_event_id: 'evt1', authorize_expires_at: new Date() };
          const userId = new Types.ObjectId().toHexString();

          jest.spyOn(service, 'findByDeviceId').mockResolvedValue(null);
          jest.spyOn(service, 'create').mockResolvedValue({ _id: new Types.ObjectId(), ...droneDto } as any);
          jest.spyOn(droneAuthorizationService, 'getLatestAuthorizationForDrone').mockResolvedValue(null);
          jest.spyOn(droneAuthorizationService, 'create').mockResolvedValue({ _id: new Types.ObjectId() } as any);

          const result = await service.createOrUpdateDroneWithAuthorization(droneDto, authDto, userId);

          expect(result.drone).toBeDefined();
          expect(result.authorization).toBeDefined();
          expect(service.create).toHaveBeenCalled();
          expect(droneAuthorizationService.create).toHaveBeenCalled();
      });
  });

});
