import { Test, TestingModule } from '@nestjs/testing';
import { DroneAuthorizationService } from './droneAuthorization.service';
import { getModelToken } from '@nestjs/mongoose';
import { AppSyncService } from '../../utils/appsync.service';
import { OrganizationService } from '../organizations/organization.service';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { LoggingService } from '../logging/logging.service';
import { DroneService } from '../drones/drone.service';
import { NotificationService } from '../alertNotifications/notification.service';
import { Model, Types } from 'mongoose';
import { DroneAuthorization } from './droneAuthorization.interface';
import Constants from '../../common/constants';

describe('DroneAuthorizationService', () => {
  let service: DroneAuthorizationService;
  let model: Model<DroneAuthorization>;
  let notificationService: NotificationService;

  const mockModel = jest.fn().mockImplementation((dto) => ({
    ...dto,
    save: jest.fn().mockResolvedValue({_id: new Types.ObjectId(), ...dto}),
  }));

  Object.assign(mockModel, {
    find: jest.fn(),
    findOne: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    countDocuments: jest.fn(),
  });

  const mockAppSyncService = { publishMessage: jest.fn() };
  const mockOrganizationService = { findById: jest.fn() };
  const mockCacheManager = { get: jest.fn(), set: jest.fn(), del: jest.fn() };
  const mockLoggingService = { createLog: jest.fn() };
  const mockDroneService = { findOne: jest.fn() };
  const mockNotificationService = { findByEventIdAndOrgId: jest.fn(), update: jest.fn(), findAllActiveEventsByOrgId: jest.fn().mockResolvedValue([]) };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DroneAuthorizationService,
        { provide: getModelToken(Constants.droneAuthorizations), useValue: mockModel },
        { provide: AppSyncService, useValue: mockAppSyncService },
        { provide: OrganizationService, useValue: mockOrganizationService },
        { provide: CACHE_MANAGER, useValue: mockCacheManager },
        { provide: LoggingService, useValue: mockLoggingService },
        { provide: DroneService, useValue: mockDroneService },
        { provide: NotificationService, useValue: mockNotificationService },
      ],
    }).compile();

    service = module.get<DroneAuthorizationService>(DroneAuthorizationService);
    model = module.get<Model<DroneAuthorization>>(getModelToken(Constants.droneAuthorizations));
    notificationService = module.get<NotificationService>(NotificationService);

    (mockModel as any).find.mockReturnValue({
      sort: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      exec: jest.fn().mockResolvedValue([]),
    } as any);
    (mockModel as any).findOne.mockReturnValue({ exec: jest.fn().mockResolvedValue({}) } as any);
    (mockModel as any).findByIdAndUpdate.mockReturnValue({ exec: jest.fn().mockResolvedValue({}) } as any);
    (mockModel as any).countDocuments.mockReturnValue({ exec: jest.fn().mockResolvedValue(0) } as any);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create and save a new drone authorization', async () => {
      const authDto = { drone_id: new Types.ObjectId(), authorized_by: 'user' };
      const userId = new Types.ObjectId().toHexString();
      
      const result = await service.create(authDto, userId);
      
      expect(result).toBeDefined();
      expect(mockLoggingService.createLog).toHaveBeenCalled();
    });
  });

  describe('findOne', () => {
    it('should find a drone authorization by id', async () => {
      const authId = new Types.ObjectId().toHexString();
      const mockAuth = { _id: authId };
      
      (mockModel as any).findOne.mockReturnValue({ exec: jest.fn().mockResolvedValue(mockAuth) });

      const result = await service.findOne(authId);

      expect(result).toEqual(mockAuth);
      expect((mockModel as any).findOne).toHaveBeenCalledWith({ _id: authId, isDeleted: false });
    });
  });
  
  describe('delete', () => {
    it('should soft delete an authorization', async () => {
      const authId = new Types.ObjectId().toHexString();
      const userId = new Types.ObjectId().toHexString();
      const mockAuth = { _id: authId, drone_id: new Types.ObjectId() };
      
      jest.spyOn(model, 'findOne').mockReturnValue({ exec: jest.fn().mockResolvedValue(mockAuth) } as any);
      jest.spyOn(model, 'findByIdAndUpdate').mockReturnValue({ exec: jest.fn().mockResolvedValue(mockAuth) } as any);
      jest.spyOn(notificationService, 'findByEventIdAndOrgId').mockResolvedValue([]);
      
      const result = await service.delete(authId, 'event-id', userId);
      
      expect(model.findByIdAndUpdate).toHaveBeenCalled();
      expect(result.message).toContain('deleted successfully');
    });
  });
});
