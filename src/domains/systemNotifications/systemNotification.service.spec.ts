import { Test, TestingModule } from '@nestjs/testing';
import { SystemNotificationService } from './systemNotification.service';
import { getModelToken } from '@nestjs/mongoose';
import { OrganizationService } from '../organizations/organization.service';
import { AppSyncService } from '../../utils/appsync.service';
import { Model, Types } from 'mongoose';
import { SystemNotification } from './systemNotification.interface';

describe('SystemNotificationService', () => {
  let service: SystemNotificationService;
  let model: Model<SystemNotification>;
  let organizationService: OrganizationService;

  const mockModel = jest.fn().mockImplementation((dto) => ({
    ...dto,
    save: jest.fn().mockResolvedValue(dto),
  }));

  Object.assign(mockModel, {
    find: jest.fn(),
    countDocuments: jest.fn(),
    updateOne: jest.fn(),
    updateMany: jest.fn(),
  });

  const mockOrganizationService = {
    findByAuth0Id: jest.fn(),
    findById: jest.fn(),
  };
  
  const mockAppSyncService = {
      publishMessage: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SystemNotificationService,
        { provide: getModelToken('system_notification'), useValue: mockModel },
        { provide: OrganizationService, useValue: mockOrganizationService },
        { provide: AppSyncService, useValue: mockAppSyncService },
      ],
    }).compile();

    service = module.get<SystemNotificationService>(SystemNotificationService);
    model = module.get<Model<SystemNotification>>(getModelToken('system_notification'));
    organizationService = module.get<OrganizationService>(OrganizationService);

    (mockModel as any).find.mockReturnValue({
        sort: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue([]),
    } as any);
    (mockModel as any).countDocuments.mockResolvedValue(0 as any);
    (mockModel as any).updateOne.mockResolvedValue({ matchedCount: 1, acknowledged: true, modifiedCount: 1, upsertedCount: 0, upsertedId: null } as any);
    (mockModel as any).updateMany.mockResolvedValue({ matchedCount: 1, acknowledged: true, modifiedCount: 1, upsertedCount: 0, upsertedId: null } as any);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createNotification', () => {
    it('should create a new notification', async () => {
      const orgId = 'org-1';
      const notificationDto = { message: 'Test' };
      mockOrganizationService.findByAuth0Id.mockResolvedValue({ _id: new Types.ObjectId() });
      
      const result = await service.createNotification(notificationDto as any, orgId);
      expect(result).toEqual(notificationDto);
    });
  });

  describe('findAllUnseenForUser', () => {
    it('should find unseen notifications for a user', async () => {
        const user = { _id: new Types.ObjectId(), created_at: new Date() };
        const orgId = 'org-1';
        mockOrganizationService.findByAuth0Id.mockResolvedValue({ _id: new Types.ObjectId() });
        (mockModel as any).countDocuments.mockResolvedValue(0);

        const result = await service.findAllUnseenForUser(user, orgId);
        expect(result.notifications).toEqual([]);
        expect(result.pagination.total).toBe(0);
    });
  });

  describe('markAsSeen', () => {
    it('should mark a notification as seen by a user', async () => {
        const id = new Types.ObjectId().toHexString();
        const userId = new Types.ObjectId();
        (mockModel as any).updateOne.mockResolvedValue({ matchedCount: 1 });
        const result = await service.markAsSeen(id, userId);
        expect(result.matchedCount).toBe(1);
    });
  });
});
