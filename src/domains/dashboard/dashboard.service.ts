import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import Constants from 'src/common/constants';
import { OrganizationService } from '../organizations/organization.service';
import { NotificationTypeEnum } from 'src/common/enums/NotifocationTypeEnum';

@Injectable()
export class DashboardService {
  private readonly logger = new Logger(DashboardService.name);

  constructor(
    @InjectModel(Constants.drones) private readonly droneModel: Model<any>,
    @InjectModel(Constants.alertZones) private readonly alertZoneModel: Model<any>,
    @InjectModel(Constants.droneAuthorizations) private readonly droneAuthorizationModel: Model<any>,
    @InjectModel('logs') private readonly logModel: Model<any>,
    @InjectModel(Constants.notification) private readonly notificationModel: Model<any>,
    private organizationService: OrganizationService,
    
  ) {}

  async getDashboardStats(orgId: string, alertZoneIds: string[], startDate: string, endDate: string, uasId: string[], groupBy: 'daily' | 'weekly' | 'monthly' | 'yearly' = 'daily'): Promise<any> {
    try {
    const orgObjectId = (await this.organizationService.findByAuth0Id(orgId))._id;

    const startDateObj = new Date(startDate);
    const endDateObj = new Date(endDate);
    const isSpectrumFlagTrue = process.env.SPECTRUM_FLAG === "true";

    // if start and end date is null, set to beginning and end of the year
    if (!startDate || !endDate) {
      startDateObj.setFullYear(new Date().getFullYear(), 0, 1);
      endDateObj.setFullYear(new Date().getFullYear(), 11, 31);
    }

    const totalDays = Math.ceil((endDateObj.getTime() - startDateObj.getTime()) / (1000 * 60 * 60 * 24));

    // Helper function to get date format and grouping based on groupBy parameter
    const getDateGrouping = (groupBy: string) => {
      switch (groupBy) {
        case 'daily':
          return {
            format: "%Y-%m-%d",
            groupField: {
              $dateToString: { format: "%Y-%m-%d", date: "$timestamp", timezone: "America/New_York" }
            }
          };
        case 'weekly':
          return {
            format: "%Y-W%U", // Year-Week format
            groupField: {
              $concat: [
                { $toString: { $year: "$timestamp" } },
                "-W",
                { $toString: { $week: "$timestamp" } }
              ]
            }
          };
        case 'monthly':
          return {
            format: "%Y-%m",
            groupField: {
              $dateToString: { format: "%Y-%m", date: "$timestamp", timezone: "America/New_York" }
            }
          };
        case 'yearly':
          return {
            format: "%Y",
            groupField: {
              $dateToString: { format: "%Y", date: "$timestamp", timezone: "America/New_York" }
            }
          };
        default:
          return {
            format: "%Y-%m-%d",
            groupField: {
              $dateToString: { format: "%Y-%m-%d", date: "$timestamp", timezone: "America/New_York" }
            }
          };
      }
    };

    const dateGrouping = getDateGrouping(groupBy);

    // Helper function to create base match stage
    const createBaseMatch = () => ({
      $match: {
        $and: [
          { org_id: orgObjectId },
          { type: isSpectrumFlagTrue? {$in: [NotificationTypeEnum.ADMIN, NotificationTypeEnum.RID_ALERT, NotificationTypeEnum.SPECTRUM_ALERT]} : { $ne: NotificationTypeEnum.RID_ALERT } },
          { "alertZone._id": { $in: alertZoneIds } },
          {
            timestamp: {
              $gte: startDateObj,
              $lte: endDateObj
            }
          }
        ]
      }
    });

    // Helper function to create lookup stage
    const createLookupStage = () => ({
      $lookup: {
        from: 'event_profile',
        localField: 'event_id',
        foreignField: 'EVENT_ID',
        as: 'result'
      }
    });

    // Helper function to build aggregation pipeline
    const buildPipeline = (stages: any[]) => {
      return stages.filter(stage => stage && Object.keys(stage).length > 0);
    };

      const aggregation1 = buildPipeline([
        createBaseMatch(),
        createLookupStage(),
        ...(uasId && uasId.length > 0 ? [{ $match: { 'result.UAS_ID': { $in: uasId } } }] : []),
        { $count: "matchedCount" }
      ]);

      const aggregation2 = buildPipeline([
        createBaseMatch(),
        createLookupStage(),
        { $match: { result: { $ne: [] } } },
        ...(uasId && uasId.length > 0 ? [{ $match: { 'result.UAS_ID': { $in: uasId } } }] : []),
        { $unwind: "$result" },
        { $group: { _id: "$result.UAS_ID" } },
        { $count: "matchedCount" }
      ]);

      const aggregation3 = buildPipeline([
        createBaseMatch(),
        createLookupStage(),
        { $match: { result: { $ne: [] } } },
        ...(uasId && uasId.length > 0 ? [{ $match: { 'result.UAS_ID': { $in: uasId } } }] : []),
        { $unwind: "$result" },
        {
          $group: {
            _id: null,
            avgDuration: { $avg: "$result.DURATION" }
          }
        }
      ]);

      // const aggregation4 = buildPipeline([
      //   createBaseMatch(),
      //   createLookupStage(),
      //   ...(uasId && uasId.length > 0 ? [{ $match: { 'result.UAS_ID': { $in: uasId } } }] : []),
      //   {
      //     $project: {
      //       dateOnly: {
      //         $dateToString: { format: "%Y-%m-%d", date: "$timestamp", timezone: "America/New_York" }
      //       }
      //     }
      //   },
      //   {
      //     $group: {
      //       _id: "$dateOnly",
      //       name: { $first: "$dateOnly" },
      //       eventCount: { $sum: 1 }
      //     }
      //   },
      //   {
      //     $group: {
      //       _id: null,
      //       averageEventsPerDay: { $avg: "$eventCount" },
      //       totalDays: { $sum: 1 },
      //       totalEvents: { $sum: "$eventCount" }
      //     }
      //   }
      // ]);

      const aggregation5 = buildPipeline([
        createBaseMatch(),
        createLookupStage(),
        ...(uasId && uasId.length > 0 ? [{ $match: { 'result.UAS_ID': { $in: uasId } } }] : []),
        {
          $project: {
            dateGrouped: dateGrouping.groupField
          }
        },
        {
          $group: {
            _id: "$dateGrouped",
            name: { $first: "$dateGrouped" },
            eventCount: { $sum: 1 }
          }
        },
        {
          $sort: {
            '_id': 1 as const
          }
        }
      ]);

      const aggregation6 = buildPipeline([
        createBaseMatch(),
        createLookupStage(),
        { $match: { result: { $ne: [] } } },
        ...(uasId && uasId.length > 0 ? [{ $match: { 'result.UAS_ID': { $in: uasId } } }] : []),
        { $unwind: "$result" },
        {
          $group: {
            _id: "$result.UAS_ID",
            name: { $first: "$result.UAS_ID" },
            eventCount: { $sum: 1 }
          }
        }
      ]);

      const aggregation7 = buildPipeline([
        createBaseMatch(),
        createLookupStage(),
        ...(uasId && uasId.length > 0 ? [{ $match: { 'result.UAS_ID': { $in: uasId } } }] : []),
        {
          $group: {
            _id: "$alertZone._id",
            name: { $first: "$alertZone.name" },
            eventCount: { $sum: 1 }
          }
        }
      ]);

      const aggregation8 = buildPipeline([
        createBaseMatch(),
        createLookupStage(),
        ...(uasId && uasId.length > 0 ? [{ $match: { 'result.UAS_ID': { $in: uasId } } }] : []),
        {
          $project: {
            hourOnly: {
              $dateToString: { format: "%H", date: "$timestamp", timezone: "America/New_York" }
            }
          }
        },
        {
          $group: {
            _id: "$hourOnly",
            name: { $first: {$toInt:"$hourOnly"} },
            eventCount: { $sum: 1 }
          }
        },
        {
          $sort: {
            'name': 1 as const
          }
        }
      ]);

      // Execute all aggregations in parallel
      const [
        result1,
        result2,
        result3,
        // result4,
        result5,
        result6,
        result7,
        result8
      ] = await Promise.all([
        this.notificationModel.aggregate(aggregation1).exec(),
        this.notificationModel.aggregate(aggregation2).exec(),
        this.notificationModel.aggregate(aggregation3).exec(),
        // this.notificationModel.aggregate(aggregation4).exec(),
        this.notificationModel.aggregate(aggregation5).exec(),
        this.notificationModel.aggregate(aggregation6).exec(),
        this.notificationModel.aggregate(aggregation7).exec(),
        this.notificationModel.aggregate(aggregation8).exec()
      ]);
      // Calculate average events per period based on groupBy
      let averageEventsPerPeriod: number;
      let periodLabel: string;

      switch (groupBy) {
        case 'weekly':
          const totalWeeks = Math.ceil(totalDays / 7);
          averageEventsPerPeriod = (result1[0]?.matchedCount || 0) / totalWeeks;
          periodLabel = 'averageEventsPerWeek';
          break;
        case 'monthly':
          const totalMonths = Math.ceil(totalDays / 30); // Approximate
          averageEventsPerPeriod = (result1[0]?.matchedCount || 0) / totalMonths;
          periodLabel = 'averageEventsPerMonth';
          break;
        case 'yearly':
          const totalYears = Math.ceil(totalDays / 365); // Approximate
          averageEventsPerPeriod = (result1[0]?.matchedCount || 0) / totalYears;
          periodLabel = 'averageEventsPerYear';
          break;
        default: // daily
          averageEventsPerPeriod = (result1[0]?.matchedCount || 0) / totalDays;
          periodLabel = 'averageEventsPerDay';
          break;
      }

      return {
        totalEvents: result1[0]?.matchedCount || 0,
        uniqueDrones: result2[0]?.matchedCount || 0,
        averageDuration: result3[0]?.avgDuration || 0,
        [periodLabel]: averageEventsPerPeriod,
        groupedEvents: result5 || [],
        groupBy: groupBy,
        dronesPerUasId: result6 || [],
        eventsPerAlertZones: result7 || [],
        eventsPerHour: result8 || []
      };
    } catch (error) {
      this.logger.error('Error getting dashboard stats:', error);
      throw error;
    }
  }

}
