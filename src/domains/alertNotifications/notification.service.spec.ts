import { Test, TestingModule } from '@nestjs/testing';
import { NotificationService } from './notification.service';
import { getModelToken } from '@nestjs/mongoose';
import { OrganizationService } from '../organizations/organization.service';
import { Model } from 'mongoose';
import { Notification } from './notification.interface';
import Constants from '../../common/constants';

describe('NotificationService', () => {
  let service: NotificationService;
  let organizationService: OrganizationService;
  let notificationModel: Model<Notification>;

  const mockNotificationModel = {
    aggregate: jest.fn(),
    findById: jest.fn(),
    find: jest.fn(),
    findByIdAndUpdate: jest.fn(),
  };

  const mockOrganizationService = {
    findByAuth0Id: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NotificationService,
        {
          provide: getModelToken(Constants.notification),
          useValue: mockNotificationModel,
        },
        {
          provide: OrganizationService,
          useValue: mockOrganizationService,
        },
      ],
    }).compile();

    service = module.get<NotificationService>(NotificationService);
    organizationService = module.get<OrganizationService>(OrganizationService);
    notificationModel = module.get<Model<Notification>>(getModelToken(Constants.notification));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findAllActiveEventsByOrgId', () => {
    it('should call the aggregation pipeline with the correct parameters', async () => {
      const orgId = 'auth0-org-id';
      const realOrgId = { _id: 'mongo-org-id' };
      const exec = jest.fn().mockResolvedValue([]);
      mockOrganizationService.findByAuth0Id.mockResolvedValue(realOrgId);
      mockNotificationModel.aggregate.mockReturnValue({ exec });

      await service.findAllActiveEventsByOrgId(orgId);

      expect(mockOrganizationService.findByAuth0Id).toHaveBeenCalledWith(orgId);
      expect(mockNotificationModel.aggregate).toHaveBeenCalled();
      expect(exec).toHaveBeenCalled();
    });
  });

  describe('findOne', () => {
    it('should call findById with the correct id', async () => {
      const notificationId = 'notification-id';
      await service.findOne(notificationId);
      expect(mockNotificationModel.findById).toHaveBeenCalledWith(notificationId);
    });
  });

  describe('findByEventIdAndOrgId', () => {
    it('should find notifications by event and organization id', async () => {
      const eventId = 'event-id';
      const orgId = 'auth0-org-id';
      const realOrgId = { _id: 'mongo-org-id' };
      mockOrganizationService.findByAuth0Id.mockResolvedValue(realOrgId);

      await service.findByEventIdAndOrgId(eventId, orgId);

      expect(mockOrganizationService.findByAuth0Id).toHaveBeenCalledWith(orgId);
      expect(mockNotificationModel.find).toHaveBeenCalledWith({
        event_id: eventId,
        org_id: realOrgId._id,
      });
    });
  });

  describe('update', () => {
    it('should update a notification', async () => {
      const id = 'notification-id';
      const data = { seen: true };
      await service.update(id, data);
      expect(mockNotificationModel.findByIdAndUpdate).toHaveBeenCalledWith(id, data, { new: true });
    });
  });
});
