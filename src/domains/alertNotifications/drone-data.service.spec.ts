import prepareDroneDetectionData from './drone-data.service';
import { NotificationTypeEnum } from '../../common/enums/NotifocationTypeEnum';

describe('prepareDroneDetectionData', () => {
  const mockEvent = {
    EVENT_ID: 'evt-123',
    UAS_ID: 'uas-456',
    INFO: {
      LAT: 34.0522,
      LON: -118.2437,
      ALTITUDE: 100,
      SPEED: 50,
      INFO: {
        ODID_basicID_uaType: 2, // Helicopter/Multirotor
        ODID_loc_status: 2, // Airborne
        ODID_operator_type: 1, // Dynamic
        ODID_system_lat: 34.0525,
        ODID_system_lon: -118.244,
        ODID_loc_direction: 90,
      },
    },
    COMPLETE: 0,
  };

  const mockAlertZone = {
    _id: 'zone-789',
    name: 'Test Zone',
  };

  it('should be defined', () => {
    expect(prepareDroneDetectionData).toBeDefined();
  });

  it('should correctly prepare drone detection data with default RID_ALERT type', () => {
    const droneAuthId = 'auth-abc';
    const result = prepareDroneDetectionData(mockEvent, mockAlertZone, droneAuthId);

    expect(result.alertZoneId).toBe(mockAlertZone._id);
    expect(result.alertZoneName).toBe(mockAlertZone.name);
    expect(result.eventId).toBe(mockEvent.EVENT_ID);
    expect(result.uasId).toBe(mockEvent.UAS_ID);
    expect(result.uasType).toBe('Helicopter/Multirotor');
    expect(result.droneLat).toBe(mockEvent.INFO.LAT);
    expect(result.droneLng).toBe(mockEvent.INFO.LON);
    expect(result.pilotLat).toBe(mockEvent.INFO.INFO.ODID_system_lat);
    expect(result.pilotLng).toBe(mockEvent.INFO.INFO.ODID_system_lon);
    expect(result.locationStatus).toBe('Airborne');
    expect(result.altitude).toBe(mockEvent.INFO.ALTITUDE);
    expect(result.speed).toBe(mockEvent.INFO.SPEED);
    expect(result.heading).toBe(mockEvent.INFO.INFO.ODID_loc_direction);
    expect(result.operatorType).toBe('Dynamic');
    expect(result.complete).toBe(mockEvent.COMPLETE);
    expect(result.event).toEqual(mockEvent);
    expect(result.droneAuthId).toBe(droneAuthId);
    expect(result.type).toBe(NotificationTypeEnum.RID_ALERT);
  });

  it('should allow overriding the notification type', () => {
    const result = prepareDroneDetectionData(mockEvent, mockAlertZone, null, NotificationTypeEnum.SPECTRUM_ALERT);
    expect(result.type).toBe(NotificationTypeEnum.SPECTRUM_ALERT);
  });

  it('should handle missing or undefined event info gracefully', () => {
    const simpleEvent = {
      EVENT_ID: 'evt-simple',
      UAS_ID: 'uas-simple',
      INFO: {},
      COMPLETE: 1,
    };
    const result = prepareDroneDetectionData(simpleEvent, mockAlertZone);

    expect(result.uasType).toBe('N/A');
    expect(result.locationStatus).toBeUndefined();
    expect(result.operatorType).toBe('N/A');
    expect(result.pilotLat).toBe('N/A');
    expect(result.pilotLng).toBe('N/A');
    expect(result.heading).toBe('N/A');
    expect(result.droneLat).toBeUndefined();
  });
});
