import { Test, TestingModule } from '@nestjs/testing';
import { AdminService } from './admin.service';
import { CacheManagementService } from '../../utils/cache-management.service';
import { DroneService } from '../drones/drone.service';
import { CACHE_MANAGER } from '@nestjs/cache-manager';

describe('AdminService', () => {
  let service: AdminService;
  let cacheManagementService: CacheManagementService;
  let droneService: DroneService;
  let cacheManager: any;

  beforeEach(async () => {
    const mockCacheManager = {
      del: jest.fn(),
      get: jest.fn(),
      set: jest.fn(),
    };

    const mockCacheManagementService = {
      clearSpecificKeys: jest.fn(),
      clearCacheByPatterns: jest.fn(),
      resetAllCache: jest.fn(),
      getCacheStats: jest.fn(),
    };

    const mockDroneService = {
      findOne: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AdminService,
        {
          provide: CACHE_MANAGER,
          useValue: mockCacheManager,
        },
        {
          provide: CacheManagementService,
          useValue: mockCacheManagementService,
        },
        {
          provide: DroneService,
          useValue: mockDroneService,
        },
      ],
    }).compile();

    service = module.get<AdminService>(AdminService);
    cacheManagementService = module.get<CacheManagementService>(CacheManagementService);
    droneService = module.get<DroneService>(DroneService);
    cacheManager = module.get(CACHE_MANAGER);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getSystemHealth', () => {
    it('should return system health information', async () => {
      const cacheStats = { totalKeys: 10 };
      jest.spyOn(cacheManagementService, 'getCacheStats').mockResolvedValue({
        success: true,
        stats: cacheStats,
        message: 'Stats retrieved',
      });

      const result = await service.getSystemHealth();

      expect(result.success).toBe(true);
      expect(result.health).toBeDefined();
      expect(result.health.uptime).toBeGreaterThan(0);
      expect(result.health.memory).toBeDefined();
      expect(result.health.cache).toEqual(cacheStats);
    });
  });

  describe('clearAllCache', () => {
    it('should clear all defined cache patterns and specific keys', async () => {
      jest.spyOn(cacheManagementService, 'clearSpecificKeys').mockResolvedValue({
        success: true,
        clearedKeys: ['auth0_management_token'],
        message: 'Cleared',
      });
      jest.spyOn(cacheManagementService, 'clearCacheByPatterns').mockResolvedValue({
        success: true,
        clearedKeys: ['dev:drone:auth:123'],
        message: 'Cleared',
      });

      const result = await service.clearAllCache();

      expect(result.success).toBe(true);
      expect(result.message).toContain('Successfully cleared 2 cache entries');
      expect(cacheManagementService.clearSpecificKeys).toHaveBeenCalled();
      expect(cacheManagementService.clearCacheByPatterns).toHaveBeenCalled();
    });
  });

  describe('clearDroneCache', () => {
    it('should clear drone-specific cache keys', async () => {
      const droneId = 'test-drone';
      const orgId = 'test-org';
      const droneData = { device_id: 'device-123' };

      jest.spyOn(droneService, 'findOne').mockResolvedValue(droneData as any);
      jest.spyOn(cacheManager, 'del').mockResolvedValue(undefined);

      const result = await service.clearDroneCache(droneId, orgId);

      expect(result.success).toBe(true);
      expect(result.message).toBe(`Successfully cleared cache for drone ${droneId}`);
      expect(droneService.findOne).toHaveBeenCalledWith(droneId);
      expect(cacheManager.del).toHaveBeenCalledTimes(4);
    });
  });
});
