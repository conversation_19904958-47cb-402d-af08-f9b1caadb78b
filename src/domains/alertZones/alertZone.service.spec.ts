import { Test, TestingModule } from '@nestjs/testing';
import { AlertZoneService } from './alertZone.service';
import { getModelToken } from '@nestjs/mongoose';
import { H3Service } from '../../utils/h3.service';
import { ServiceZoneService } from '../serviceZone/serviceZone.service';
import { OrganizationService } from '../organizations/organization.service';
import { LoggingService } from '../logging/logging.service';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Model, Types } from 'mongoose';
import { AlertZone } from './alertZone.interface';

describe('AlertZoneService', () => {
  let service: AlertZoneService;
  let alertZoneModel: Model<AlertZone>;
  let organizationService: OrganizationService;
  let serviceZoneService: ServiceZoneService;

  const mockAlertZoneModel = jest.fn().mockImplementation(() => ({
    save: jest.fn(),
  }));

  Object.assign(mockAlertZoneModel, {
    find: jest.fn(),
    findOne: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    updateOne: jest.fn(),
    aggregate: jest.fn(),
  });

  const mockUser = {
    _id: new Types.ObjectId(),
    org_id: 'auth0-org-id',
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AlertZoneService,
        { provide: getModelToken('alertZone'), useValue: mockAlertZoneModel },
        { provide: H3Service, useValue: { getH3Index: jest.fn() } },
        { provide: ServiceZoneService, useValue: { findServiceZonesFromH3Indexes: jest.fn(), createServiceZoneWithGPS: jest.fn() } },
        { provide: OrganizationService, useValue: { findByAuth0Id: jest.fn() } },
        { provide: LoggingService, useValue: { logAlertZoneStatusChange: jest.fn(), createLog: jest.fn() } },
        { provide: CACHE_MANAGER, useValue: { get: jest.fn(), set: jest.fn() } },
      ],
    }).compile();

    service = module.get<AlertZoneService>(AlertZoneService);
    alertZoneModel = module.get<Model<AlertZone>>(getModelToken('alertZone'));
    organizationService = module.get<OrganizationService>(OrganizationService);
    serviceZoneService = module.get<ServiceZoneService>(ServiceZoneService);

    // Mock the chained 'exec' call for query builders
    (mockAlertZoneModel as any).find.mockReturnValue({ exec: jest.fn().mockResolvedValue([]) } as any);
    (mockAlertZoneModel as any).findOne.mockResolvedValue(null);
    (mockAlertZoneModel as any).findByIdAndUpdate.mockResolvedValue(null);
    (mockAlertZoneModel as any).updateOne.mockReturnValue({ exec: jest.fn().mockResolvedValue({ matchedCount: 1 }) } as any);
    (mockAlertZoneModel as any).aggregate.mockReturnValue({ exec: jest.fn().mockResolvedValue([]) } as any);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createOrganizationAlertZone', () => {
    it('should create an alert zone for an organization', async () => {
      const alertZoneDto = {
        name: 'Org Zone',
        geometry: {
          type: 'Polygon',
          coordinates: [[[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]]]
        }
      };
      const org = { _id: new Types.ObjectId() };
      const savedAlertZone = {
        _id: new Types.ObjectId(),
        ...alertZoneDto,
        orgId: org,
        geometry: alertZoneDto.geometry
      };

      jest.spyOn(organizationService, 'findByAuth0Id').mockResolvedValue(org as any);
      jest.spyOn(serviceZoneService, 'findServiceZonesFromH3Indexes').mockResolvedValue([{_id: new Types.ObjectId()} as any]);

      (mockAlertZoneModel as any).mockImplementation((dto) => ({
        ...dto,
        geometry: dto.geometry,
        save: jest.fn().mockResolvedValue(savedAlertZone),
      }));

      const result = await service.createOrganizationAlertZone(alertZoneDto, mockUser as any);

      expect(organizationService.findByAuth0Id).toHaveBeenCalledWith(mockUser.org_id);
      expect(result).toBeDefined();
      expect(result.orgId).toEqual(org);
    });
  });

  describe('findZonesForOrganization', () => {
    it('should find alert zones for a specific organization', async () => {
      const orgId = 'auth0-org-id';
      await service.findZonesForOrganization(orgId);
      expect((mockAlertZoneModel as any).aggregate).toHaveBeenCalled();
    });
  });

  describe('deleteAlertZoneById', () => {
    it('should soft delete an alert zone', async () => {
      const zoneId = new Types.ObjectId().toHexString();
      const mockZone = { _id: zoneId, name: 'Test', orgId: 'org1' };
      
      (mockAlertZoneModel as any).findOne.mockResolvedValue(mockZone);
      (mockAlertZoneModel as any).updateOne.mockReturnValue({ exec: jest.fn().mockResolvedValue({ matchedCount: 1 }) } as any);

      const result = await service.deleteAlertZoneById(zoneId, mockUser._id.toHexString());

      expect((mockAlertZoneModel as any).findOne).toHaveBeenCalledWith({ _id: new Types.ObjectId(zoneId), isDeleted: false });
      expect(result.message).toContain('soft deleted successfully');
    });
  });

});
