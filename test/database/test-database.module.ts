import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule } from '@nestjs/config';
import Constants from 'src/common/constants';

// Import all schemas
import alertZoneSchema from 'src/domains/alertZones/alertZone.schema';
import DroneSchema from 'src/domains/drones/drone.schema';
import DroneAuthorizationSchema from 'src/domains/droneAuthorizations/droneAuthorization.schema';
import SensorSchema from 'src/domains/sensors/sensor.schema';
import ServiceZoneSchema from 'src/domains/serviceZone/serviceZone.schema';
import OrganizationSchema from 'src/domains/organizations/organization.schema';
import { UserSchema } from 'src/domains/users/user.schema';
import EventProfileSchema from 'src/domains/events/eventProfile.schema';
import EventHistorySchema from 'src/domains/events/eventHistory.schema';
import NotificationSchema from 'src/domains/alertNotifications/notification.schema';
import SystemNotificationSchema from 'src/domains/systemNotifications/systemNotification.schema';
import UserPreferencesSchema from 'src/domains/userPreferences/userPreferences.schema';
import LogSchema from 'src/domains/logging/log.schema';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    // Main database connection for testing
    MongooseModule.forRoot(
      process.env.TEST_MONGODB_CONNECTION_STRING || '***********************************************************************',
      {
        dbName: process.env.TEST_DATABASE_NAME || 'coddn_test',
        connectionName: 'default',
      }
    ),
    // Register all schemas for testing
    MongooseModule.forFeature([
      { name: Constants.alertZones, schema: alertZoneSchema },
      { name: Constants.drones, schema: DroneSchema },
      { name: Constants.droneAuthorizations, schema: DroneAuthorizationSchema },
      { name: Constants.sensorProfile, schema: SensorSchema },
      { name: 'servicezones', schema: ServiceZoneSchema },
      { name: Constants.organizations, schema: OrganizationSchema },
      { name: 'User', schema: UserSchema },
      { name: Constants.event_profile, schema: EventProfileSchema },
      { name: Constants.event_history, schema: EventHistorySchema },
      { name: Constants.notification, schema: NotificationSchema },
      { name: Constants.system_notification, schema: SystemNotificationSchema },
      { name: Constants.userPreferences, schema: UserPreferencesSchema },
      { name: 'logs', schema: LogSchema },
    ]),
  ],
  exports: [MongooseModule],
})
export class TestDatabaseModule {}
