import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule } from '@nestjs/config';
import Constants from 'src/common/constants';
import SpectrumEventSchema from 'src/domains/spectrumEvents/spectrumEvent.schema';
import SpectrumEventHistorySchema from 'src/domains/spectrumEvents/spectrumEventHistory.schema';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    // Spectrum database connection for testing
    MongooseModule.forRoot(
      process.env.TEST_SPECTRUM_MONGODB_CONNECTION_STRING || '**************************************************************************',
      {
        dbName: process.env.TEST_SPECTRUM_DATABASE_NAME || 'spectrum_test',
        connectionName: 'spectrum',
      }
    ),
    // Register spectrum schemas for testing
    MongooseModule.forFeature([
      {
        name: Constants.spectrum_profile_events,
        schema: SpectrumEventSchema,
        collection: Constants.spectrum_profile_events
      },
      {
        name: Constants.spectrum_history_events,
        schema: SpectrumEventHistorySchema,
        collection: Constants.spectrum_history_events
      },
    ], 'spectrum'),
  ],
  exports: [MongooseModule],
})
export class TestSpectrumDatabaseModule {}
