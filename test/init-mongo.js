// MongoDB initialization script for e2e tests
print('Starting MongoDB initialization for e2e tests...');

// Switch to the test database
db = db.getSiblingDB('coddn_test');

// Create collections with proper indexes
print('Creating collections and indexes...');

// Organizations collection
db.createCollection('organizations');
db.organizations.createIndex({ "auth0_id": 1 });
db.organizations.createIndex({ "name": 1 });

// Users collection
db.createCollection('users');
db.users.createIndex({ "sub": 1 }, { unique: true });
db.users.createIndex({ "email": 1 });
db.users.createIndex({ "org_id": 1 });

// Alert Zones collection
db.createCollection('alertzones');
db.alertzones.createIndex({ "orgId": 1 });
db.alertzones.createIndex({ "userId": 1 });
db.alertzones.createIndex({ "name": 1 });

// Drones collection
db.createCollection('drones');
db.drones.createIndex({ "device_id": 1 }, { unique: true });
db.drones.createIndex({ "org_id": 1, "device_id": 1, "uas_id": 1, "operator_id": 1 });

// Drone Authorizations collection
db.createCollection('drone_authorizations');
db.drone_authorizations.createIndex({ "drone_id": 1, "createdAt": -1, "is_authorized": 1 });

// Sensors collection
db.createCollection('sensor_profile');
db.sensor_profile.createIndex({ "NODE_ID": 1 });
db.sensor_profile.createIndex({ "HOST_NAME": 1 });

// Service Zones collection
db.createCollection('servicezones');
db.servicezones.createIndex({ "name": 1 }, { unique: true });

// Events collections
db.createCollection('event_profile');
db.event_profile.createIndex({ "EVENT_ID": 1 });

db.createCollection('event_history');
db.event_history.createIndex({ "EVENT_ID": 1 });

// Notifications collection
db.createCollection('notification');
db.notification.createIndex({ "org_id": 1 });
db.notification.createIndex({ "event_id": 1 });

// System notifications collection
db.createCollection('system_notification');

// User preferences collection
db.createCollection('userPreferences');

// Logs collection
db.createCollection('logs');

print('MongoDB initialization completed successfully!');
