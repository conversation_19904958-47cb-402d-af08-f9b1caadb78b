// Global setup for e2e tests
import { TestSetup } from './utils/test-setup';

// Set longer timeout for e2e tests
jest.setTimeout(120000);

// Global teardown
afterAll(async () => {
  console.log('🧹 Global e2e test cleanup...');
  await TestSetup.cleanup();
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.ENABLE_CACHE = 'false';
process.env.TEST_MONGODB_CONNECTION_STRING = '***********************************************************************';
process.env.TEST_DATABASE_NAME = 'coddn_test';
process.env.TEST_SPECTRUM_MONGODB_CONNECTION_STRING = '**************************************************************************';
process.env.TEST_SPECTRUM_DATABASE_NAME = 'spectrum_test';
