import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { Types } from 'mongoose';
import { TestSetup, E2ETestContext } from './utils/test-setup';
import { SeededData } from './utils/database-seeder';
import { AlertZoneModule } from '../src/domains/alertZones/alertZone.module';
import { ServiceZoneModule } from '../src/domains/serviceZone/serviceZone.module';
import { OrganizationModule } from '../src/domains/organizations/organization.module';
import { LoggingModule } from '../src/domains/logging/logging.module';
import { AlertZoneStatusEnum } from '../src/common/enums/AlertZoneStatusEnum';

describe('AlertZones (e2e)', () => {
  let context: E2ETestContext;
  let app: INestApplication;
  let seededData: SeededData;

  beforeAll(async () => {
    console.log('🚀 Setting up AlertZones e2e tests...');
    
    // Setup e2e test environment with required modules
    context = await TestSetup.setupE2ETest([
      AlertZoneModule,
      ServiceZoneModule,
      OrganizationModule,
      LoggingModule
    ]);
    
    app = context.app;
    
    // Wait for database connection
    await TestSetup.waitForDatabaseConnection(context);
    
    // Seed test data
    seededData = await TestSetup.seedTestData(context);
    
    console.log('✅ AlertZones e2e test setup completed');
  }, 120000); // 2 minute timeout for setup

  afterAll(async () => {
    console.log('🧹 Tearing down AlertZones e2e tests...');
    await TestSetup.teardownE2ETest(context);
  }, 30000);

  beforeEach(async () => {
    // Clear and reseed data before each test to ensure clean state
    seededData = await TestSetup.seedTestData(context);
  });

  describe('GET /alert-zones', () => {
    it('should return all alert zones for an organization', async () => {
      const orgId = seededData.organizations[0]._id.toString();
      
      const response = await request(app.getHttpServer())
        .get('/alert-zones')
        .query({ orgId })
        .expect(200);

      expect(response.body).toBeDefined();
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      
      // Verify the alert zone structure
      const alertZone = response.body[0];
      expect(alertZone).toHaveProperty('_id');
      expect(alertZone).toHaveProperty('name');
      expect(alertZone).toHaveProperty('orgId');
      expect(alertZone).toHaveProperty('geometry');
      expect(alertZone).toHaveProperty('latestStatus');
      expect(alertZone.orgId).toBe(orgId);
    });

    it('should return empty array for organization with no alert zones', async () => {
      const nonExistentOrgId = new Types.ObjectId().toString();
      
      const response = await request(app.getHttpServer())
        .get('/alert-zones')
        .query({ orgId: nonExistentOrgId })
        .expect(200);

      expect(response.body).toBeDefined();
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBe(0);
    });

    it('should return 400 for missing orgId parameter', async () => {
      await request(app.getHttpServer())
        .get('/alert-zones')
        .expect(400);
    });
  });

  describe('POST /alert-zones', () => {
    it('should create a new alert zone', async () => {
      const orgId = seededData.organizations[0]._id.toString();
      const serviceZoneId = seededData.serviceZones[0]._id.toString();
      
      const newAlertZone = {
        name: 'Test Alert Zone E2E',
        orgId: orgId,
        serviceZones: [serviceZoneId],
        geometry: {
          type: 'Polygon',
          coordinates: [[
            [-74.0059, 40.7128],
            [-74.0059, 40.7228],
            [-73.9959, 40.7228],
            [-73.9959, 40.7128],
            [-74.0059, 40.7128]
          ]]
        }
      };

      const response = await request(app.getHttpServer())
        .post('/alert-zones')
        .send(newAlertZone)
        .expect(201);

      expect(response.body).toBeDefined();
      expect(response.body).toHaveProperty('_id');
      expect(response.body.name).toBe(newAlertZone.name);
      expect(response.body.orgId).toBe(orgId);
      expect(response.body.latestStatus).toBe(AlertZoneStatusEnum.INACTIVE);
    });

    it('should return 400 for invalid alert zone data', async () => {
      const invalidAlertZone = {
        name: '', // Empty name should be invalid
        orgId: 'invalid-org-id',
        geometry: {
          type: 'InvalidType', // Invalid geometry type
          coordinates: []
        }
      };

      await request(app.getHttpServer())
        .post('/alert-zones')
        .send(invalidAlertZone)
        .expect(400);
    });
  });

  describe('GET /alert-zones/:id', () => {
    it('should return a specific alert zone by id', async () => {
      const alertZoneId = seededData.alertZones[0]._id.toString();

      const response = await request(app.getHttpServer())
        .get(`/alert-zones/${alertZoneId}`)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body._id).toBe(alertZoneId);
      expect(response.body).toHaveProperty('name');
      expect(response.body).toHaveProperty('geometry');
    });

    it('should return 404 for non-existent alert zone', async () => {
      const nonExistentId = new Types.ObjectId().toString();

      await request(app.getHttpServer())
        .get(`/alert-zones/${nonExistentId}`)
        .expect(404);
    });

    it('should return 400 for invalid ObjectId format', async () => {
      await request(app.getHttpServer())
        .get('/alert-zones/invalid-id')
        .expect(400);
    });
  });

  describe('PUT /alert-zones/:id', () => {
    it('should update an existing alert zone', async () => {
      const alertZoneId = seededData.alertZones[0]._id.toString();
      const updatedName = 'Updated Alert Zone Name';

      const updateData = {
        name: updatedName,
        latestStatus: AlertZoneStatusEnum.ACTIVE
      };

      const response = await request(app.getHttpServer())
        .put(`/alert-zones/${alertZoneId}`)
        .send(updateData)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body._id).toBe(alertZoneId);
      expect(response.body.name).toBe(updatedName);
      expect(response.body.latestStatus).toBe(AlertZoneStatusEnum.ACTIVE);
    });

    it('should return 404 when updating non-existent alert zone', async () => {
      const nonExistentId = new Types.ObjectId().toString();

      await request(app.getHttpServer())
        .put(`/alert-zones/${nonExistentId}`)
        .send({ name: 'Updated Name' })
        .expect(404);
    });
  });

  describe('DELETE /alert-zones/:id', () => {
    it('should soft delete an alert zone', async () => {
      const alertZoneId = seededData.alertZones[0]._id.toString();

      await request(app.getHttpServer())
        .delete(`/alert-zones/${alertZoneId}`)
        .expect(200);

      // Verify the alert zone is soft deleted (not returned in normal queries)
      const response = await request(app.getHttpServer())
        .get(`/alert-zones/${alertZoneId}`)
        .expect(404);
    });

    it('should return 404 when deleting non-existent alert zone', async () => {
      const nonExistentId = new Types.ObjectId().toString();

      await request(app.getHttpServer())
        .delete(`/alert-zones/${nonExistentId}`)
        .expect(404);
    });
  });

  describe('Database Integration', () => {
    it('should persist data correctly in MongoDB', async () => {
      // Verify data exists in database
      const alertZoneCount = await context.connection.db
        .collection('alertzones')
        .countDocuments({ isDeleted: { $ne: true } });

      expect(alertZoneCount).toBeGreaterThan(0);
      expect(alertZoneCount).toBe(seededData.alertZones.length);
    });

    it('should maintain referential integrity with organizations', async () => {
      const alertZones = await context.connection.db
        .collection('alertzones')
        .find({ isDeleted: { $ne: true } })
        .toArray();

      for (const alertZone of alertZones) {
        const orgExists = await context.connection.db
          .collection('organizations')
          .findOne({ _id: alertZone.orgId });

        expect(orgExists).toBeTruthy();
      }
    });
  });
});
