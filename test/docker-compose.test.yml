version: '3.8'

services:
  mongodb-test:
    image: mongo:7.0
    container_name: coddn-test-mongodb
    ports:
      - "27018:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: testuser
      MONGO_INITDB_ROOT_PASSWORD: testpass
      MONGO_INITDB_DATABASE: coddn_test
    volumes:
      - mongodb_test_data:/data/db
      - ./init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
    networks:
      - coddn-test-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  mongodb-spectrum-test:
    image: mongo:7.0
    container_name: coddn-spectrum-test-mongodb
    ports:
      - "27019:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: testuser
      MONGO_INITDB_ROOT_PASSWORD: testpass
      MONGO_INITDB_DATABASE: spectrum_test
    volumes:
      - mongodb_spectrum_test_data:/data/db
    networks:
      - coddn-test-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

volumes:
  mongodb_test_data:
  mongodb_spectrum_test_data:

networks:
  coddn-test-network:
    driver: bridge
