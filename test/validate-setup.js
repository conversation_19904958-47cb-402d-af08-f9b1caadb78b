#!/usr/bin/env node

/**
 * Validation script for e2e testing setup
 * Run this to verify all components are working correctly
 */

const { exec } = require('child_process');
const { promisify } = require('util');
const fs = require('fs');
const path = require('path');

const execAsync = promisify(exec);

async function validateSetup() {
  console.log('🔍 Validating E2E Testing Setup...\n');

  const checks = [
    checkDockerAvailable,
    checkRequiredFiles,
    checkPackageScripts,
    checkDependencies,
    checkJestConfiguration
  ];

  let allPassed = true;

  for (const check of checks) {
    try {
      await check();
    } catch (error) {
      console.error(`❌ ${error.message}`);
      allPassed = false;
    }
  }

  if (allPassed) {
    console.log('\n✅ All validation checks passed!');
    console.log('\n🚀 You can now run e2e tests with:');
    console.log('   npm run test:e2e');
    console.log('\n📚 See test/README.md for detailed documentation');
  } else {
    console.log('\n❌ Some validation checks failed. Please fix the issues above.');
    process.exit(1);
  }
}

async function checkDockerAvailable() {
  console.log('🐳 Checking Docker availability...');
  
  try {
    await execAsync('docker --version');
    await execAsync('docker-compose --version');
    console.log('✅ Docker and Docker Compose are available');
  } catch (error) {
    throw new Error('Docker or Docker Compose not found. Please install Docker Desktop.');
  }
}

async function checkRequiredFiles() {
  console.log('📁 Checking required files...');
  
  const requiredFiles = [
    'test/docker-compose.test.yml',
    'test/init-mongo.js',
    'test/jest-e2e.json',
    'test/jest.setup.e2e.ts',
    'test/database/test-database.module.ts',
    'test/database/test-spectrum-database.module.ts',
    'test/fixtures/seed-data.factory.ts',
    'test/utils/docker-manager.ts',
    'test/utils/database-seeder.ts',
    'test/utils/test-setup.ts',
    'test/alertZones.e2e-spec.ts',
    'test/README.md'
  ];

  for (const file of requiredFiles) {
    if (!fs.existsSync(path.join(__dirname, '..', file))) {
      throw new Error(`Required file missing: ${file}`);
    }
  }
  
  console.log('✅ All required files are present');
}

async function checkPackageScripts() {
  console.log('📦 Checking package.json scripts...');
  
  const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, '..', 'package.json'), 'utf8'));
  const requiredScripts = [
    'test:e2e',
    'test:e2e:watch',
    'test:e2e:debug',
    'docker:test:up',
    'docker:test:down',
    'docker:test:logs'
  ];

  for (const script of requiredScripts) {
    if (!packageJson.scripts[script]) {
      throw new Error(`Required npm script missing: ${script}`);
    }
  }
  
  console.log('✅ All required npm scripts are present');
}

async function checkDependencies() {
  console.log('📚 Checking dependencies...');
  
  const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, '..', 'package.json'), 'utf8'));
  const requiredDevDeps = [
    '@testcontainers/mongodb',
    'testcontainers',
    'mongodb-memory-server'
  ];

  for (const dep of requiredDevDeps) {
    if (!packageJson.devDependencies[dep]) {
      throw new Error(`Required dev dependency missing: ${dep}. Run: npm install --save-dev ${dep}`);
    }
  }
  
  console.log('✅ All required dependencies are installed');
}

async function checkJestConfiguration() {
  console.log('⚙️ Checking Jest configuration...');
  
  const jestConfig = JSON.parse(fs.readFileSync(path.join(__dirname, 'jest-e2e.json'), 'utf8'));
  
  const requiredConfig = {
    testTimeout: 120000,
    maxWorkers: 1,
    forceExit: true,
    detectOpenHandles: true
  };

  for (const [key, value] of Object.entries(requiredConfig)) {
    if (jestConfig[key] !== value) {
      throw new Error(`Jest configuration issue: ${key} should be ${value}, got ${jestConfig[key]}`);
    }
  }
  
  console.log('✅ Jest configuration is correct');
}

// Run validation
validateSetup().catch(error => {
  console.error('❌ Validation failed:', error.message);
  process.exit(1);
});
