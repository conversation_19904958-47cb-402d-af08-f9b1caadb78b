import { exec } from 'child_process';
import { promisify } from 'util';
import * as path from 'path';

const execAsync = promisify(exec);

export class DockerManager {
  private static readonly COMPOSE_FILE = path.join(__dirname, '..', 'docker-compose.test.yml');
  private static readonly PROJECT_NAME = 'coddn-e2e-test';

  static async startContainers(): Promise<void> {
    console.log('🐳 Starting Docker containers for e2e tests...');
    
    try {
      // Stop any existing containers first
      await this.stopContainers();
      
      // Start the containers
      const { stdout, stderr } = await execAsync(
        `docker-compose -f ${this.COMPOSE_FILE} -p ${this.PROJECT_NAME} up -d --build --force-recreate`
      );
      
      if (stderr && !stderr.includes('Creating') && !stderr.includes('Starting')) {
        console.warn('Docker compose stderr:', stderr);
      }
      
      console.log('✅ Docker containers started successfully');
      
      // Wait for containers to be healthy
      await this.waitForHealthyContainers();
      
    } catch (error) {
      console.error('❌ Failed to start Docker containers:', error);
      throw error;
    }
  }

  static async stopContainers(): Promise<void> {
    console.log('🛑 Stopping Docker containers...');
    
    try {
      await execAsync(
        `docker-compose -f ${this.COMPOSE_FILE} -p ${this.PROJECT_NAME} down -v --remove-orphans`
      );
      console.log('✅ Docker containers stopped successfully');
    } catch (error) {
      // Don't throw error if containers are already stopped
      console.warn('⚠️ Warning while stopping containers:', error.message);
    }
  }

  static async waitForHealthyContainers(maxWaitTime: number = 60000): Promise<void> {
    console.log('⏳ Waiting for containers to be healthy...');
    
    const startTime = Date.now();
    const checkInterval = 2000; // Check every 2 seconds
    
    while (Date.now() - startTime < maxWaitTime) {
      try {
        const { stdout } = await execAsync(
          `docker-compose -f ${this.COMPOSE_FILE} -p ${this.PROJECT_NAME} ps --format json`
        );
        
        const containers = stdout.trim().split('\n').map(line => JSON.parse(line));
        const allHealthy = containers.every(container => 
          container.Health === 'healthy' || container.State === 'running'
        );
        
        if (allHealthy) {
          console.log('✅ All containers are healthy');
          return;
        }
        
        console.log('⏳ Containers still starting up...');
        await new Promise(resolve => setTimeout(resolve, checkInterval));
        
      } catch (error) {
        console.log('⏳ Waiting for containers to start...');
        await new Promise(resolve => setTimeout(resolve, checkInterval));
      }
    }
    
    throw new Error(`Containers did not become healthy within ${maxWaitTime}ms`);
  }

  static async getContainerLogs(serviceName: string): Promise<string> {
    try {
      const { stdout } = await execAsync(
        `docker-compose -f ${this.COMPOSE_FILE} -p ${this.PROJECT_NAME} logs ${serviceName}`
      );
      return stdout;
    } catch (error) {
      console.error(`Failed to get logs for ${serviceName}:`, error);
      return '';
    }
  }

  static async cleanupVolumes(): Promise<void> {
    console.log('🧹 Cleaning up Docker volumes...');
    
    try {
      await execAsync(
        `docker volume prune -f --filter label=com.docker.compose.project=${this.PROJECT_NAME}`
      );
      console.log('✅ Docker volumes cleaned up');
    } catch (error) {
      console.warn('⚠️ Warning while cleaning up volumes:', error.message);
    }
  }
}
