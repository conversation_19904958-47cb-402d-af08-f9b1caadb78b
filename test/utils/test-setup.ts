import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { Connection } from 'mongoose';
import { getConnectionToken } from '@nestjs/mongoose';
import { DockerManager } from './docker-manager';
import { DatabaseSeeder, SeededData } from './database-seeder';
import { TestDatabaseModule } from '../database/test-database.module';
import { TestSpectrumDatabaseModule } from '../database/test-spectrum-database.module';

export interface E2ETestContext {
  app: INestApplication;
  moduleRef: TestingModule;
  connection: Connection;
  seeder: DatabaseSeeder;
  seededData?: SeededData;
}

export class TestSetup {
  private static isDockerStarted = false;

  static async setupE2ETest(additionalModules: any[] = []): Promise<E2ETestContext> {
    // Start Docker containers if not already started
    if (!this.isDockerStarted) {
      await DockerManager.startContainers();
      this.isDockerStarted = true;
      
      // Set up cleanup on process exit
      process.on('exit', () => this.cleanup());
      process.on('SIGINT', () => this.cleanup());
      process.on('SIGTERM', () => this.cleanup());
      process.on('uncaughtException', () => this.cleanup());
    }

    // Set test environment variables
    process.env.NODE_ENV = 'test';
    process.env.TEST_MONGODB_CONNECTION_STRING = '***********************************************************************';
    process.env.TEST_DATABASE_NAME = 'coddn_test';
    process.env.TEST_SPECTRUM_MONGODB_CONNECTION_STRING = '**************************************************************************';
    process.env.TEST_SPECTRUM_DATABASE_NAME = 'spectrum_test';
    process.env.ENABLE_CACHE = 'false';

    // Create test module
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        TestDatabaseModule,
        TestSpectrumDatabaseModule,
        ...additionalModules
      ],
    }).compile();

    // Create application
    const app = moduleFixture.createNestApplication();
    await app.init();

    // Get database connection
    const connection = moduleFixture.get<Connection>(getConnectionToken());

    // Create database seeder
    const seeder = new DatabaseSeeder(moduleFixture);

    return {
      app,
      moduleRef: moduleFixture,
      connection,
      seeder
    };
  }

  static async teardownE2ETest(context: E2ETestContext): Promise<void> {
    console.log('🧹 Tearing down e2e test...');
    
    try {
      // Clear database
      await context.seeder.clearAllCollections();
      
      // Close application
      await context.app.close();
      
      // Close database connection
      await context.connection.close();
      
      console.log('✅ E2E test teardown completed');
    } catch (error) {
      console.error('❌ Error during e2e test teardown:', error);
    }
  }

  static async cleanup(): Promise<void> {
    console.log('🧹 Cleaning up Docker containers...');
    
    try {
      await DockerManager.stopContainers();
      await DockerManager.cleanupVolumes();
      this.isDockerStarted = false;
    } catch (error) {
      console.error('❌ Error during cleanup:', error);
    }
  }

  static async seedTestData(context: E2ETestContext): Promise<SeededData> {
    console.log('🌱 Seeding test data...');
    const seededData = await context.seeder.seedCompleteDataset();
    context.seededData = seededData;
    return seededData;
  }

  static async waitForDatabaseConnection(context: E2ETestContext, maxWaitTime: number = 10000): Promise<void> {
    console.log('⏳ Waiting for database connection...');
    
    const startTime = Date.now();
    
    while (Date.now() - startTime < maxWaitTime) {
      try {
        await context.connection.db.admin().ping();
        console.log('✅ Database connection established');
        return;
      } catch (error) {
        console.log('⏳ Waiting for database...');
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    throw new Error(`Database connection not established within ${maxWaitTime}ms`);
  }
}
